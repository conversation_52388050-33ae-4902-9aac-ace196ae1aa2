package com.caelisgroups.azelle.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.caelisgroups.azelle.AuthManager
import com.caelisgroups.azelle.AuthResponse
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WriteBioScreen(
    authManager: AuthManager,
    onBioSaved: (String) -> Unit,
    onBack: () -> Unit
) {
    var bioText by remember { mutableStateOf("") }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }
    val maxCharacters = 500

    // Sauvegarder l'étape dès qu'on arrive sur cette page (une seule fois)
    LaunchedEffect(Unit) {
        try {
            authManager.updateOnboardingStep("bio").collect { }
        } catch (e: Exception) {
            // Ignorer les erreurs de sauvegarde d'étape
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Header avec bouton retour
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Retour",
                    tint = Color.Black
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Titre
        Text(
            text = "Veuillez vous\nprésenter",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            modifier = Modifier.fillMaxWidth()
        )

        Text(
            text = "Écrivez tout ce qui peut vous décrire",
            fontSize = 14.sp,
            color = Color.Gray,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Exemple de texte
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 24.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF5F5F5)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Ex) Heyy ! 😊 J'adore écouter de la musique en me promenant. Et le chocolat à la menthe ? Jamais de la vie ! 😅 Si je devais me décrire, je suis un vrai « rêveur curieux ». Si tu veux prendre ton temps et discuter, parlons ! 💕",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    lineHeight = 20.sp
                )
            }
        }

        // Zone de texte pour la bio
        OutlinedTextField(
            value = bioText,
            onValueChange = { newText ->
                if (newText.length <= maxCharacters) {
                    bioText = newText
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            placeholder = {
                Text(
                    text = "Écrivez votre présentation ici...",
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            },
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFE91E63),
                unfocusedBorderColor = Color.Gray,
                cursorColor = Color(0xFFE91E63)
            ),
            shape = RoundedCornerShape(12.dp),
            maxLines = 8
        )

        // Compteur de caractères
        Text(
            text = "${bioText.length}/$maxCharacters",
            fontSize = 12.sp,
            color = if (bioText.length >= maxCharacters) Color.Red else Color.Gray,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            textAlign = TextAlign.End
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Avertissement
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFFF3E0)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Text(
                text = "Mentionner vos autres comptes de plateforme de messagerie peut vous exposer à des dangers tels que la fraude, le harcèlement sexuel et le blasphème.",
                fontSize = 12.sp,
                color = Color(0xFF795548),
                lineHeight = 16.sp,
                modifier = Modifier.padding(16.dp)
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // Boutons Retour et Terminé
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier
                    .weight(1f)
                    .height(50.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Black
                ),
                border = androidx.compose.foundation.BorderStroke(1.dp, Color.Gray)
            ) {
                Text("Retour", fontSize = 16.sp)
            }

            Spacer(modifier = Modifier.width(16.dp))

            Button(
                onClick = {
                    scope.launch {
                        isLoading = true
                        try {
                            // Sauvegarder la bio
                            authManager.updateUserBio(bioText.trim())
                            onBioSaved(bioText.trim())
                        } catch (e: Exception) {
                            // Gérer l'erreur
                        } finally {
                            isLoading = false
                        }
                    }
                },
                modifier = Modifier
                    .weight(1f)
                    .height(50.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE91E63)
                ),
                enabled = !isLoading && bioText.trim().isNotEmpty()
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                } else {
                    Text("Terminé", fontSize = 16.sp, color = Color.White)
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}
