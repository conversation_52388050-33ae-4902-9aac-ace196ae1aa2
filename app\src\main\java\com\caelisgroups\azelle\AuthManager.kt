package com.caelisgroups.azelle

import android.content.Context
import android.util.Log
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import io.github.jan.supabase.auth.Auth
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.auth.providers.Google
import io.github.jan.supabase.auth.providers.builtin.Email
import io.github.jan.supabase.auth.providers.builtin.IDToken
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.postgrest
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.postgrest.query.PostgrestRequestBuilder
import io.github.jan.supabase.storage.Storage
import io.github.jan.supabase.storage.storage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.security.MessageDigest
import java.util.UUID
import android.net.Uri
import java.io.InputStream
import kotlinx.serialization.json.*

sealed interface AuthResponse {
    data object Success : AuthResponse
    data class Error(val message: String?) : AuthResponse
}

// Data class pour les langues d'intérêt avec niveau
data class InterestLanguage(
    val name: String,
    val level: Int
)

class AuthManager(
    private val context: Context
) {

    val supabase = createSupabaseClient(
        supabaseUrl = "https://wbkkdsdcxmavnovnotel.supabase.co",
        supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6India2tkc2RjeG1hdm5vdm5vdGVsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MzM0NDcsImV4cCI6MjA2NjIwOTQ0N30.9fv0XaJj0IFPP3pFQIJBAb2JwGESJN9T-k0WSFrBuO8"
    ) {
        install(Auth) {
            // Activer la persistance de session
            alwaysAutoRefresh = true
            autoSaveToStorage = true
        }
        install(Postgrest)
        install(Storage)
    }

    // Fonction pour initialiser et restaurer la session
    suspend fun initializeSession() {
        try {
            Log.d("AuthManager", "initializeSession: Tentative de restauration de session...")
            // Supabase va automatiquement restaurer la session si elle existe
            val user = supabase.auth.currentUserOrNull()
            Log.d("AuthManager", "initializeSession: user après init = $user")
            Log.d("AuthManager", "initializeSession: user.id après init = ${user?.id}")

            // Petit délai pour laisser le temps à Supabase de restaurer la session
            kotlinx.coroutines.delay(500)

            val userAfterDelay = supabase.auth.currentUserOrNull()
            Log.d("AuthManager", "initializeSession: user après délai = $userAfterDelay")
        } catch (e: Exception) {
            Log.e("AuthManager", "initializeSession: Erreur lors de l'initialisation", e)
        }
    }

    fun signUpWithEmail(emailValue: String, passwordValue: String): Flow<AuthResponse> = flow {
        try {
            supabase.auth.signUpWith(Email) {
                email = emailValue
                password = passwordValue
            }

            emit(AuthResponse.Success)
        } catch (e: Exception) {
            emit(AuthResponse.Error(e.localizedMessage))
        }
    }

    fun signInWithEmail(emailValue: String, passwordValue: String): Flow<AuthResponse> = flow {
        try {
            supabase.auth.signInWith(Email) {
                email = emailValue
                password = passwordValue
            }
            emit(AuthResponse.Success)

        } catch (e: Exception) {
            emit(AuthResponse.Error(e.localizedMessage))
        }
    }

    fun createNonce(): String {
        val rawNonce = UUID.randomUUID().toString()
        val bytes = rawNonce.toByteArray()
        val md = MessageDigest.getInstance("SHA-256")
        val digest = md.digest(bytes)

        return digest.fold("") { str, it ->
            str + "%02x".format(it)
        }
    }

    fun loginGoogleUser(): Flow<AuthResponse> = flow {
        val hashedNonce = createNonce()

        val googleIdOption = GetGoogleIdOption.Builder()
            .setServerClientId("************-nampj23qno7d5c9ofp2qvn7kv7t7sjct.apps.googleusercontent.com")
            .setNonce(hashedNonce)
            .setAutoSelectEnabled(false)
            .setFilterByAuthorizedAccounts(false)
            .build()

        val request = GetCredentialRequest.Builder()
            .addCredentialOption(googleIdOption)
            .build()

        val credentialManager = CredentialManager.create(context)

        try {
            val result = credentialManager.getCredential(
                context = context,
                request = request
            )

            val googleIdTokenCredential = GoogleIdTokenCredential
                .createFrom(result.credential.data)

            val googleIdToken = googleIdTokenCredential.idToken

            supabase.auth.signInWith(IDToken) {
                idToken = googleIdToken
                provider = Google
            }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            Log.e("google", e.localizedMessage)
            emit(AuthResponse.Error(e.localizedMessage))
        }
    }

    fun signInWithGoogleIdToken(idToken: String): Flow<AuthResponse> = flow {
        try {
            Log.d("AuthManager", "signInWithGoogleIdToken: Début connexion avec idToken")

            supabase.auth.signInWith(IDToken) {
                this.idToken = idToken
                provider = Google
            }

            val userId = supabase.auth.currentUserOrNull()?.id
            Log.d("AuthManager", "signInWithGoogleIdToken: Connexion réussie, userId = $userId")

            // Vérifier si un profil existe déjà
            val profileExists = checkIfProfileExists(userId)
            Log.d("AuthManager", "signInWithGoogleIdToken: Profil existe = $profileExists")

            if (!profileExists && userId != null) {
                Log.d("AuthManager", "signInWithGoogleIdToken: Création d'un profil vide pour Google Sign-In")
                // Créer un profil vide pour les utilisateurs Google
                createEmptyProfile(userId)
            }

            emit(AuthResponse.Success)
        } catch (e: Exception) {
            Log.e("AuthManager", "signInWithGoogleIdToken: Erreur = ${e.localizedMessage}", e)
            emit(AuthResponse.Error(e.localizedMessage))
        }
    }

    fun updateProfile(name: String, gender: String, birthday: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            Log.d("AuthManager", "updateProfile: userId = $userId")
            Log.d("AuthManager", "updateProfile: name = $name, gender = $gender, birthday = $birthday")

            if (userId != null) {
                // Utiliser UPSERT pour créer ou mettre à jour le profil
                val result = supabase.postgrest["profiles"]
                    .upsert(
                        mapOf(
                            "id" to userId,
                            "full_name" to name,
                            "gender" to gender,
                            "birthday" to birthday,
                            "onboarding_step" to "photo"
                        )
                    )

                Log.d("AuthManager", "updateProfile: Résultat upsert = $result")
                emit(AuthResponse.Success)
            } else {
                emit(AuthResponse.Error("Utilisateur non connecté"))
            }
        } catch (e: Exception) {
            Log.e("AuthManager", "updateProfile: Erreur lors de la sauvegarde", e)
            emit(AuthResponse.Error(e.localizedMessage))
        }
    }

    fun completePhotoStep(): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            Log.d("AuthManager", "completePhotoStep: userId = $userId")

            if (userId != null) {
                // Utiliser UPDATE car le profil existe déjà à ce stade
                val result = supabase.postgrest["profiles"]
                    .update(
                        mapOf(
                            "onboarding_step" to "complete"
                        )
                    ) {
                        filter { eq("id", userId) }
                    }

                Log.d("AuthManager", "completePhotoStep: Résultat update = $result")
                emit(AuthResponse.Success)
            } else {
                emit(AuthResponse.Error("Utilisateur non connecté"))
            }
        } catch (e: Exception) {
            Log.e("AuthManager", "completePhotoStep: Erreur lors de la mise à jour", e)
            emit(AuthResponse.Error(e.localizedMessage))
        }
    }

    suspend fun isProfileComplete(): Boolean {
        return try {
            val user = supabase.auth.currentUserOrNull()
            Log.d("AuthManager", "Utilisateur connecté: id=${user?.id}, email=${user?.email}")
            val userId = user?.id

            if (userId == null) {
                Log.d("AuthManager", "isProfileComplete: Pas d'utilisateur connecté")
                return false
            }

            val response = supabase.postgrest["profiles"]
                .select {
                    filter { eq("id", userId) }
                }

            Log.d("AuthManager", "isProfileComplete: Réponse Supabase = $response")
            Log.d("AuthManager", "isProfileComplete: Type de response.data = ${response.data?.let { it::class.qualifiedName }}")

            // Essayer différents types de casting
            val profiles = when (val data = response.data) {
                is List<*> -> {
                    Log.d("AuthManager", "isProfileComplete: data est une List, taille = ${data.size}")
                    data.filterIsInstance<Map<String, Any?>>()
                }
                else -> {
                    Log.d("AuthManager", "isProfileComplete: data n'est pas une List, type = ${data?.let { it::class.qualifiedName }}")
                    emptyList()
                }
            }

            if (profiles.isEmpty()) {
                Log.d("AuthManager", "isProfileComplete: Aucun profil trouvé - profil pas complet")
                return false
            }

            val profile = profiles.first()
            Log.d("AuthManager", "isProfileComplete: Profile complet = $profile")

            // Vérifier les champs individuels ET l'onboarding_step
            val fullName = profile["full_name"]?.toString()?.trim()
            val gender = profile["gender"]?.toString()?.trim()
            val birthday = profile["birthday"]?.toString()?.trim()
            val onboardingStep = profile["onboarding_step"]?.toString()?.trim()

            Log.d("AuthManager", "isProfileComplete: fullName = '$fullName'")
            Log.d("AuthManager", "isProfileComplete: gender = '$gender'")
            Log.d("AuthManager", "isProfileComplete: birthday = '$birthday'")
            Log.d("AuthManager", "isProfileComplete: onboarding_step = '$onboardingStep'")

            // Le profil est complet si tous les champs sont remplis OU si onboarding_step n'est pas null/vide
            val fieldsComplete = !fullName.isNullOrEmpty() &&
                                !gender.isNullOrEmpty() &&
                                !birthday.isNullOrEmpty() &&
                                birthday.matches(Regex("""\d{4}-\d{2}-\d{2}"""))

            val stepExists = !onboardingStep.isNullOrEmpty()

            val isComplete = fieldsComplete || stepExists

            Log.d("AuthManager", "isProfileComplete: fieldsComplete = $fieldsComplete")
            Log.d("AuthManager", "isProfileComplete: stepExists = $stepExists")
            Log.d("AuthManager", "isProfileComplete: Profil complet = $isComplete")

            isComplete
        } catch (e: Exception) {
            Log.e("AuthManager", "isProfileComplete: Erreur lors de la vérification", e)
            false
        }
    }

    fun isLoggedIn(): Boolean {
        val user = supabase.auth.currentUserOrNull()
        Log.d("AuthManager", "isLoggedIn: user = $user")
        Log.d("AuthManager", "isLoggedIn: user.id = ${user?.id}")
        Log.d("AuthManager", "isLoggedIn: user.email = ${user?.email}")
        return user != null
    }

    fun getCurrentUserId(): String? {
        return supabase.auth.currentUserOrNull()?.id
    }



    suspend fun getOnboardingStep(): String? {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return null
            Log.d("AuthManager", "getOnboardingStep: Checking userId = $userId")

            val response = supabase.postgrest["profiles"]
                .select {
                    filter { eq("id", userId) }
                }

            Log.d("AuthManager", "getOnboardingStep: Raw response = $response")
            Log.d("AuthManager", "getOnboardingStep: Response data = ${response.data}")

            val dataString = response.data.toString()
            Log.d("AuthManager", "getOnboardingStep: Data string = '$dataString'")

            // Vérifier si la réponse est vide (pas de profil)
            if (dataString == "[]" || dataString.isEmpty() || dataString == "null") {
                Log.d("AuthManager", "getOnboardingStep: AUCUN PROFIL TROUVÉ - retour null")
                return null
            }

            // Si il y a des données, parser
            when {
                dataString.contains("\"onboarding_step\":\"complete\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found COMPLETE")
                    "complete"
                }
                dataString.contains("\"onboarding_step\":\"bio\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found BIO")
                    "bio"
                }
                dataString.contains("\"onboarding_step\":\"interests\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found INTERESTS")
                    "interests"
                }
                dataString.contains("\"onboarding_step\":\"connection_goals\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found CONNECTION_GOALS")
                    "connection_goals"
                }
                dataString.contains("\"onboarding_step\":\"interest_languages\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found INTEREST_LANGUAGES")
                    "interest_languages"
                }
                dataString.contains("\"onboarding_step\":\"native_language\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found NATIVE_LANGUAGE")
                    "native_language"
                }
                dataString.contains("\"onboarding_step\":\"nationality\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found NATIONALITY")
                    "nationality"
                }
                dataString.contains("\"onboarding_step\":\"photo\"") -> {
                    Log.d("AuthManager", "getOnboardingStep: Found PHOTO")
                    "photo"
                }
                else -> {
                    Log.d("AuthManager", "getOnboardingStep: Profil existe mais pas d'onboarding_step reconnu - retour null")
                    Log.d("AuthManager", "getOnboardingStep: DataString pour debug = '$dataString'")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e("AuthManager", "getOnboardingStep: Erreur", e)
            null
        }
    }

    // Fonction pour uploader une photo
    fun uploadPhoto(uri: Uri, fileName: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            Log.d("AuthManager", "uploadPhoto: Début upload pour $fileName")

            // Lire le fichier depuis l'URI
            val inputStream = context.contentResolver.openInputStream(uri)
            val bytes = inputStream?.readBytes()
            inputStream?.close()

            if (bytes == null) {
                emit(AuthResponse.Error("Impossible de lire le fichier"))
                return@flow
            }

            // Upload vers Supabase Storage
            val filePath = "profiles/$userId/$fileName"
            val result = supabase.storage["profile-photos"].upload(filePath, bytes)

            Log.d("AuthManager", "uploadPhoto: Upload réussi - $filePath")
            emit(AuthResponse.Success)

        } catch (e: Exception) {
            Log.e("AuthManager", "uploadPhoto: Erreur upload", e)
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur upload"))
        }
    }

    // Fonction pour sauvegarder les URLs des photos dans le profil
    fun savePhotoUrls(photoUrls: List<String>): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            Log.d("AuthManager", "savePhotoUrls: Sauvegarde ${photoUrls.size} URLs")

            // Utiliser buildJsonObject pour une sérialisation correcte
            val updateData = buildJsonObject {
                put("photo_urls", JsonArray(photoUrls.map { JsonPrimitive(it) }))
                put("onboarding_step", "nationality") // Passer à l'étape suivante
            }
            Log.d("AuthManager", "savePhotoUrls: Data = $updateData")

            val result = supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            Log.d("AuthManager", "savePhotoUrls: Sauvegarde réussie")
            emit(AuthResponse.Success)

        } catch (e: Exception) {
            Log.e("AuthManager", "savePhotoUrls: Erreur sauvegarde", e)
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur sauvegarde"))
        }
    }

    // Fonction pour sauvegarder la nationalité
    fun saveNationality(nationality: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            val updateData = buildJsonObject {
                put("nationality", nationality)
                put("onboarding_step", "native_language")
            }

            supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur sauvegarde nationalité"))
        }
    }

    // Fonction pour sauvegarder la langue natale
    fun saveNativeLanguage(language: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            val updateData = buildJsonObject {
                put("native_language", language)
                put("onboarding_step", "interest_languages")
            }

            supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur sauvegarde langue natale"))
        }
    }

    // Fonction pour sauvegarder les langues d'intérêt
    fun saveInterestLanguages(languages: List<InterestLanguage>): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            val languagesJson = JsonArray(
                languages.map { lang ->
                    buildJsonObject {
                        put("name", lang.name)
                        put("level", lang.level)
                    }
                }
            )

            val updateData = buildJsonObject {
                put("interest_languages", languagesJson)
                put("onboarding_step", "connection_goals")
            }

            supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur sauvegarde langues d'intérêt"))
        }
    }

    // Fonction pour sauvegarder l'objectif de connexion (OBLIGATOIRE)
    fun saveConnectionGoal(goal: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            val updateData = buildJsonObject {
                put("connection_goal", goal)
                put("onboarding_step", "interests") // Passer aux intérêts
            }

            supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur sauvegarde objectif"))
        }
    }

    // Fonction pour récupérer les URLs des photos d'un utilisateur
    suspend fun getUserPhotoUrls(userId: String? = null): List<String> {
        return try {
            val targetUserId = userId ?: supabase.auth.currentUserOrNull()?.id
            if (targetUserId == null) {
                Log.e("AuthManager", "getUserPhotoUrls: Aucun utilisateur connecté")
                return emptyList()
            }

            Log.d("AuthManager", "getUserPhotoUrls: Récupération photos pour $targetUserId")

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("photo_urls")) {
                    filter { eq("id", targetUserId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val photoUrls = result["photo_urls"]?.jsonArray?.map { it.jsonPrimitive.content } ?: emptyList()

            Log.d("AuthManager", "getUserPhotoUrls: Trouvé ${photoUrls.size} photos")
            photoUrls

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserPhotoUrls: Erreur", e)
            emptyList()
        }
    }

    // Fonction pour générer les URLs publiques des photos
    suspend fun getPublicPhotoUrls(userId: String? = null): List<String> {
        val photoUrls = getUserPhotoUrls(userId)
        return photoUrls.map { path ->
            supabase.storage["profile-photos"].publicUrl(path)
        }
    }

    // Fonction pour mettre à jour seulement l'étape d'onboarding
    fun updateOnboardingStep(step: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            Log.d("AuthManager", "updateOnboardingStep: Mise à jour vers $step pour user $userId")

            // Utiliser la même logique que updateProfile - avec mapOf
            val result = supabase.postgrest["profiles"]
                .update(
                    mapOf(
                        "onboarding_step" to step
                    )
                ) {
                    filter { eq("id", userId) }
                }

            Log.d("AuthManager", "updateOnboardingStep: Résultat update = $result")
            emit(AuthResponse.Success)

        } catch (e: Exception) {
            Log.e("AuthManager", "updateOnboardingStep: Erreur mise à jour étape", e)
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur mise à jour étape"))
        }
    }

    // Fonction pour sauvegarder les intérêts de l'utilisateur
    suspend fun updateUserInterests(interests: Map<String, List<String>>) {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                throw Exception("Utilisateur non connecté")
            }

            Log.d("AuthManager", "updateUserInterests: Sauvegarde des intérêts pour user $userId")
            Log.d("AuthManager", "updateUserInterests: Intérêts = $interests")

            // Sauvegarder les intérêts et passer à l'étape bio
            val result = supabase.postgrest["profiles"]
                .update(
                    mapOf(
                        "interests" to interests,
                        "onboarding_step" to "bio"
                    )
                ) {
                    filter { eq("id", userId) }
                }

            Log.d("AuthManager", "updateUserInterests: Résultat update = $result")

        } catch (e: Exception) {
            Log.e("AuthManager", "updateUserInterests: Erreur sauvegarde intérêts", e)
            throw e
        }
    }

    // Fonction pour sauvegarder la bio de l'utilisateur
    suspend fun updateUserBio(bio: String) {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                throw Exception("Utilisateur non connecté")
            }

            Log.d("AuthManager", "updateUserBio: Sauvegarde de la bio pour user $userId")
            Log.d("AuthManager", "updateUserBio: Bio = $bio")

            // Sauvegarder la bio et marquer l'onboarding comme terminé
            val result = supabase.postgrest["profiles"]
                .update(
                    mapOf(
                        "bio" to bio,
                        "onboarding_step" to "complete"
                    )
                ) {
                    filter { eq("id", userId) }
                }

            Log.d("AuthManager", "updateUserBio: Résultat update = $result")

        } catch (e: Exception) {
            Log.e("AuthManager", "updateUserBio: Erreur sauvegarde bio", e)
            throw e
        }
    }

    // Fonction pour vérifier si un profil existe
    private suspend fun checkIfProfileExists(userId: String?): Boolean {
        return try {
            if (userId == null) return false

            val response = supabase.postgrest["profiles"]
                .select {
                    filter { eq("id", userId) }
                }

            val dataString = response.data.toString()
            Log.d("AuthManager", "checkIfProfileExists: Response = '$dataString'")

            !(dataString == "[]" || dataString.isEmpty() || dataString == "null")
        } catch (e: Exception) {
            Log.e("AuthManager", "checkIfProfileExists: Erreur", e)
            false
        }
    }

    // Fonction pour créer un profil vide
    private suspend fun createEmptyProfile(userId: String) {
        try {
            Log.d("AuthManager", "createEmptyProfile: Création profil pour userId = $userId")

            val result = supabase.postgrest["profiles"]
                .insert(
                    mapOf(
                        "id" to userId,
                        "onboarding_step" to null // Pas d'étape d'onboarding pour forcer le profil
                    )
                )

            Log.d("AuthManager", "createEmptyProfile: Profil créé = $result")
        } catch (e: Exception) {
            Log.e("AuthManager", "createEmptyProfile: Erreur création profil", e)
        }
    }

    // Fonction simple pour récupérer le nom de l'utilisateur
    suspend fun getUserName(): String {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return "Utilisateur"

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("full_name")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val fullName = result["full_name"]?.jsonPrimitive?.content ?: "Utilisateur"
            Log.d("AuthManager", "getUserName: Nom récupéré = $fullName")
            fullName

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserName: Erreur", e)
            "Utilisateur"
        }
    }

    // Fonction simple pour récupérer la nationalité de l'utilisateur
    suspend fun getUserNationality(): String {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return ""

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("nationality")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val nationality = result["nationality"]?.jsonPrimitive?.content ?: ""
            Log.d("AuthManager", "getUserNationality: Nationalité récupérée = $nationality")
            nationality

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserNationality: Erreur", e)
            ""
        }
    }

    // Fonction simple pour récupérer l'anniversaire de l'utilisateur
    suspend fun getUserBirthday(): String {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return ""

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("birthday")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val birthday = result["birthday"]?.jsonPrimitive?.content ?: ""
            Log.d("AuthManager", "getUserBirthday: Anniversaire récupéré = $birthday")
            birthday

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserBirthday: Erreur", e)
            ""
        }
    }

    // Fonction simple pour récupérer la bio de l'utilisateur
    suspend fun getUserBio(): String {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return ""

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("bio")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val bio = result["bio"]?.jsonPrimitive?.content ?: ""
            Log.d("AuthManager", "getUserBio: Bio récupérée = $bio")
            bio

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserBio: Erreur", e)
            ""
        }
    }

    // Fonction simple pour récupérer l'objectif de connexion
    suspend fun getUserConnectionGoal(): String {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return ""

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("connection_goal")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val goal = result["connection_goal"]?.jsonPrimitive?.content ?: ""
            Log.d("AuthManager", "getUserConnectionGoal: Objectif récupéré = $goal")
            goal

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserConnectionGoal: Erreur", e)
            ""
        }
    }

    // Fonction simple pour récupérer la langue native
    suspend fun getUserNativeLanguage(): String {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return ""

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("native_language")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val language = result["native_language"]?.jsonPrimitive?.content ?: ""
            Log.d("AuthManager", "getUserNativeLanguage: Langue native récupérée = $language")
            language

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserNativeLanguage: Erreur", e)
            ""
        }
    }

    // Fonction simple pour récupérer les langues d'intérêt
    suspend fun getUserInterestLanguages(): List<Map<String, Any>> {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return emptyList()

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("interest_languages")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val languagesJson = result["interest_languages"]?.jsonArray
            val languages = mutableListOf<Map<String, Any>>()

            languagesJson?.forEach { element ->
                val langObj = element.jsonObject
                val name = langObj["name"]?.jsonPrimitive?.content ?: ""
                val level = langObj["level"]?.jsonPrimitive?.int ?: 1
                languages.add(mapOf("name" to name, "level" to level))
            }

            Log.d("AuthManager", "getUserInterestLanguages: Langues récupérées = $languages")
            languages

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserInterestLanguages: Erreur", e)
            emptyList()
        }
    }

    // Fonction simple pour récupérer les intérêts
    suspend fun getUserInterests(): List<String> {
        return try {
            val userId = supabase.auth.currentUserOrNull()?.id ?: return emptyList()

            val result = supabase.postgrest["profiles"]
                .select(columns = Columns.list("interests")) {
                    filter { eq("id", userId) }
                }
                .decodeSingle<Map<String, JsonElement>>()

            val interestsJson = result["interests"]?.jsonArray
            val interests = mutableListOf<String>()

            interestsJson?.forEach { element ->
                element.jsonPrimitive?.content?.let { interests.add(it) }
            }

            Log.d("AuthManager", "getUserInterests: Intérêts récupérés = $interests")
            interests

        } catch (e: Exception) {
            Log.e("AuthManager", "getUserInterests: Erreur", e)
            emptyList()
        }
    }

    // Fonction pour mettre à jour le nom de l'utilisateur
    fun updateUserName(newName: String): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            val updateData = buildJsonObject {
                put("full_name", newName)
            }

            supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            Log.e("AuthManager", "Erreur mise à jour nom: ${e.message}")
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur mise à jour nom"))
        }
    }

    // Fonction pour mettre à jour les langues d'intérêt
    fun updateUserInterestLanguages(languages: List<Map<String, Any>>): Flow<AuthResponse> = flow {
        try {
            val userId = supabase.auth.currentUserOrNull()?.id
            if (userId == null) {
                emit(AuthResponse.Error("Utilisateur non connecté"))
                return@flow
            }

            val updateData = buildJsonObject {
                put("interest_languages", JsonArray(languages.map { lang ->
                    buildJsonObject {
                        put("name", lang["name"].toString())
                        put("level", lang["level"] as Int)
                    }
                }))
            }

            supabase.postgrest["profiles"]
                .update(updateData) {
                    filter { eq("id", userId) }
                }

            emit(AuthResponse.Success)

        } catch (e: Exception) {
            Log.e("AuthManager", "Erreur mise à jour langues: ${e.message}")
            emit(AuthResponse.Error(e.localizedMessage ?: "Erreur mise à jour langues"))
        }
    }
}