package com.caelisgroups.azelle.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.caelisgroups.azelle.AuthManager
import com.caelisgroups.azelle.AuthResponse
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectInterestsScreen(
    authManager: AuthManager,
    onInterestsSelected: (Map<String, List<String>>) -> Unit,
    onSkip: () -> Unit,
    onBack: () -> Unit
) {
    var selectedTab by remember { mutableStateOf("SPORT") }
    var selectedInterests by remember { mutableStateOf(setOf<String>()) }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }
    val maxSelections = 8

    val tabs = listOf("SPORT", "DIVERTISSEMENT", "NOURRITURE", "LOISIRS", "VALEURS")

    // Sauvegarder l'étape dès qu'on arrive sur cette page (une seule fois)
    LaunchedEffect(Unit) {
        try {
            authManager.updateOnboardingStep("interests").collect { }
        } catch (e: Exception) {
            // Ignorer les erreurs de sauvegarde d'étape
        }
    }

    val interestsByCategory = mapOf(
        "SPORT" to listOf(
            "⚽ Football", "🏀 Basketball", "⚾ Baseball", "🏌️ Golf", "🎾 Tennis",
            "🏊 Natation", "🏃 Course à pied", "🏸 Badminton", "🥊 Boxe", "🏄 Surf",
            "🏂 Snowboard", "⛷️ Ski", "🧗 Escalade", "🏋️ CrossFit", "🤿 Plongée",
            "🏋️ Gym", "🧘 Yoga"
        ),
        "DIVERTISSEMENT" to listOf(
            "🎬 Films d'action", "🎭 Films romantiques", "👻 Horreur", "😂 Comédie",
            "🦄 Film fantastique", "🎨 Animation", "🎭 Drame", "🎵 Comédie musicale",
            "📚 Documentaire", "🎤 Concerts", "🖼️ Expositions", "🎵 Pop", "🎵 K-pop",
            "🎵 J-pop", "👑 Rock", "🎤 Hip-hop", "🎼 Ballade", "💃 Danse", "🎷 Jazz",
            "🎼 Classique"
        ),
        "NOURRITURE" to listOf(
            "☕ Café", "🍽️ Spot gourmet", "🧁 Desserts", "🥗 Végétarien",
            "🥘 Régime", "🔍 Cuisine", "🥐 Pâtisserie", "🚚 Livraison",
            "🍺 Bière", "🍷 Vin", "🥃 Whisky", "🍵 Thé", "🍱 Faire un bento",
            "🍜 Bouffe asia", "🌮 Cuisine latine", "☕ Café drip", "🍖 Barbecue",
            "📹 Vidéos cuisine"
        ),
        "LOISIRS" to listOf(
            "🛍️ Shopping", "💅 Ongles", "🚗 Conduite", "✈️ Voyage", "🎮 Jeux",
            "🏠 Intérieur", "🎨 Dessin", "📔 Journal", "📖 Lecture",
            "🎸 Instrument", "🎤 Chanter", "📷 Photo", "🗣️ Langues", "💻 IT",
            "✏️ Ménage", "🧘 Méditation", "🚶 Marche", "🎣 Pêche", "🌱 Plantes",
            "🏕️ Camping", "🍻 Pique-nique", "❤️🔥 Fandom"
        ),
        "VALEURS" to listOf(
            "🌱 Eco", "🆕 Nouvelles expériences", "🤝 Diversité", "😊 Humble",
            "😄 Sens de l'humour", "👌 Positif", "👥 Vie sociale", "😊 Extraverti",
            "🆓 Esprit libre", "😌 Calme", "🤗 Attentionné", "😊 Gentil",
            "⚖️ Équilibre de vie", "💼 Bourreau de travail", "🤔 Curieux",
            "💭 Réfléchi", "⚡ Énergique", "👫 Amitié", "❤️ Amour", "😊 Loisirs"
        )
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
    ) {
        // Header avec bouton retour
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Retour",
                    tint = Color.Black
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Titre
        Text(
            text = "Faites-nous part de\nvos intérêts",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            modifier = Modifier.fillMaxWidth()
        )

        Text(
            text = "Modifier à tout moment après l'inscription",
            fontSize = 14.sp,
            color = Color.Gray,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Onglets
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(tabs) { tab ->
                Text(
                    text = tab,
                    fontSize = 14.sp,
                    fontWeight = if (selectedTab == tab) FontWeight.Bold else FontWeight.Normal,
                    color = if (selectedTab == tab) Color.Black else Color.Gray,
                    modifier = Modifier
                        .clickable { selectedTab = tab }
                        .padding(bottom = 4.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Compteur de sélections
        Text(
            text = "Sélectionnés: ${selectedInterests.size}/$maxSelections",
            fontSize = 14.sp,
            color = if (selectedInterests.size >= maxSelections) Color.Red else Color.Gray,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Contenu des intérêts
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            val interests = interestsByCategory[selectedTab] ?: emptyList()
            val chunkedInterests = interests.chunked(2)
            
            items(chunkedInterests) { rowInterests ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    rowInterests.forEach { interest ->
                        val isSelected = selectedInterests.contains(interest)
                        val canSelect = selectedInterests.size < maxSelections || isSelected

                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .clip(RoundedCornerShape(20.dp))
                                .background(
                                    if (isSelected) Color(0xFFE91E63) else Color.Transparent
                                )
                                .border(
                                    1.dp,
                                    if (isSelected) Color(0xFFE91E63) else Color.Gray,
                                    RoundedCornerShape(20.dp)
                                )
                                .clickable {
                                    selectedInterests = if (isSelected) {
                                        selectedInterests - interest
                                    } else if (selectedInterests.size < maxSelections) {
                                        selectedInterests + interest
                                    } else {
                                        selectedInterests
                                    }
                                }
                                .padding(12.dp)
                        ) {
                            Text(
                                text = interest,
                                fontSize = 14.sp,
                                color = if (isSelected) Color.White else Color.Black,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                    
                    // Si nombre impair, ajouter un spacer
                    if (rowInterests.size == 1) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Bouton Passer
        TextButton(
            onClick = onSkip,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        ) {
            Text(
                text = "Passer",
                color = Color.Gray,
                fontSize = 16.sp
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Boutons Retour et Suivant
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier
                    .weight(1f)
                    .height(50.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Black
                ),
                border = androidx.compose.foundation.BorderStroke(1.dp, Color.Gray)
            ) {
                Text("Retour", fontSize = 16.sp)
            }

            Spacer(modifier = Modifier.width(16.dp))

            Button(
                onClick = {
                    scope.launch {
                        isLoading = true
                        try {
                            // Sauvegarder les intérêts (même si liste vide)
                            val interestsMap = if (selectedInterests.isNotEmpty()) {
                                mapOf("selected" to selectedInterests.toList())
                            } else {
                                emptyMap<String, List<String>>()
                            }
                            authManager.updateUserInterests(interestsMap)
                            onInterestsSelected(interestsMap)
                        } catch (e: Exception) {
                            // En cas d'erreur, continuer quand même
                            onInterestsSelected(emptyMap())
                        } finally {
                            isLoading = false
                        }
                    }
                },
                enabled = !isLoading,
                modifier = Modifier
                    .weight(1f)
                    .height(50.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE91E63),
                    disabledContainerColor = Color.Gray
                )
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                } else {
                    Text("SUIVANT", fontSize = 16.sp, color = Color.White)
                }
            }
        }
    }
}
