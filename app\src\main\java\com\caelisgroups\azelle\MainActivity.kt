package com.caelisgroups.azelle

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.zIndex
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.geometry.Offset as GeometryOffset
import androidx.compose.ui.layout.ContentScale as LayoutContentScale
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.NavHostController
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import com.caelisgroups.azelle.ui.theme.AzelleTheme
import com.caelisgroups.azelle.ui.screens.SelectInterestsScreen
import com.caelisgroups.azelle.ui.screens.WriteBioScreen
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import android.content.Intent
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import java.util.Calendar
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.window.Dialog
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import androidx.compose.ui.draw.clip
import coil.compose.AsyncImage
import androidx.compose.foundation.border
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.text.TextStyle

class MainActivity : ComponentActivity() {
    private lateinit var googleSignInClient: GoogleSignInClient
    private lateinit var authManager: AuthManager
    private var onGoogleSignInSuccess: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val context = this
        authManager = AuthManager(context)

        // Configuration GoogleSignInOptions
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken("************-nampj23qno7d5c9ofp2qvn7kv7t7sjct.apps.googleusercontent.com")
            .requestEmail()
            .build()
        googleSignInClient = GoogleSignIn.getClient(this, gso)

        // Launcher pour le résultat Google Sign-In
        val googleSignInLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            val data: Intent? = result.data
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            handleSignInResult(task)
        }

        setContent {
            AzelleTheme {
                val navController = rememberNavController()
                val coroutineScope = rememberCoroutineScope()
                val context = LocalContext.current
                
                // Définir le callback pour la navigation
                onGoogleSignInSuccess = {
                    coroutineScope.launch {
                        val step = authManager.getOnboardingStep()
                        Log.d("GoogleSignIn", "onboarding_step après connexion Google = '$step'")

                        when (step) {
                            "complete" -> {
                                Log.d("GoogleSignIn", "Navigation vers home - onboarding terminé")
                                navController.navigate("home") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "connection_goals" -> {
                                Log.d("GoogleSignIn", "Navigation vers selectConnectionGoals - étape objectifs")
                                navController.navigate("selectConnectionGoals") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "interests" -> {
                                Log.d("GoogleSignIn", "Navigation vers selectInterests - étape intérêts")
                                navController.navigate("selectInterests") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "bio" -> {
                                Log.d("GoogleSignIn", "Navigation vers writeBio - étape bio")
                                navController.navigate("writeBio") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "interest_languages" -> {
                                Log.d("GoogleSignIn", "Navigation vers selectInterestLanguages - étape langues d'intérêt")
                                navController.navigate("selectInterestLanguages") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "native_language" -> {
                                Log.d("GoogleSignIn", "Navigation vers selectNativeLanguage - étape langue natale")
                                navController.navigate("selectNativeLanguage") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "nationality" -> {
                                Log.d("GoogleSignIn", "Navigation vers selectNationality - étape nationalité")
                                navController.navigate("selectNationality") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            "photo" -> {
                                Log.d("GoogleSignIn", "Navigation vers uploadPhoto - étape photo")
                                navController.navigate("uploadPhoto") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                            else -> {
                                Log.d("GoogleSignIn", "Navigation vers profile - première inscription ou profil incomplet")
                                navController.navigate("profile") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            }
                        }
                    }
                }
                
                NavHost(
                    navController = navController,
                    startDestination = "splash"
                ) {
                    composable("splash") { SplashScreen(navController, authManager) }
                    composable("welcome") {
                        WelcomeScreen(
                            onSignIn = { navController.navigate("login") },
                            onSignUp = { navController.navigate("terms") },
                            onGoogleClick = {
                                val signInIntent = googleSignInClient.signInIntent
                                googleSignInLauncher.launch(signInIntent)
                            }
                        )
                    }
                    composable("terms") {
                        TermsScreen(
                            onBack = { navController.popBackStack() },
                            onNext = { navController.navigate("signup") }
                        )
                    }
                    composable("signup") {
                        SignUpScreen(
                            onSignIn = { navController.navigate("login") },
                            onSignUp = { email, password ->
                                authManager.signUpWithEmail(email, password)
                                    .onEach { result ->
                                        when (result) {
                                            is AuthResponse.Success -> {
                                                lifecycleScope.launch {
                                                    val step = authManager.getOnboardingStep()
                                                    Log.d("SignUp", "Valeur onboarding_step = '$step'")

                                                    when (step) {
                                                        "complete" -> {
                                                            navController.navigate("home") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "connection_goals" -> {
                                                            navController.navigate("selectConnectionGoals") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "interests" -> {
                                                            navController.navigate("selectInterests") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "bio" -> {
                                                            navController.navigate("writeBio") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "interest_languages" -> {
                                                            navController.navigate("selectInterestLanguages") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "native_language" -> {
                                                            navController.navigate("selectNativeLanguage") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "nationality" -> {
                                                            navController.navigate("selectNationality") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "photo" -> {
                                                            navController.navigate("uploadPhoto") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        else -> {
                                                            navController.navigate("profile") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                    }
                                                }
                                                Toast.makeText(context, "Inscription réussie !", Toast.LENGTH_SHORT).show()
                                                Log.d("auth", "Email SignUp Success")
                                            }
                                            is AuthResponse.Error -> {
                                                Toast.makeText(context, "Erreur inscription: ${result.message}", Toast.LENGTH_LONG).show()
                                                Log.e("auth", "Email SignUp Failed: ${result.message}")
                                            }
                                        }
                                    }
                                    .launchIn(coroutineScope)
                            },
                            onFacebookClick = {
                                Toast.makeText(context, "Facebook Sign-Up non implémenté.", Toast.LENGTH_SHORT).show()
                            },
                            onGoogleClick = {
                                val signInIntent = googleSignInClient.signInIntent
                                googleSignInLauncher.launch(signInIntent)
                            }
                        )
                    }
                    composable("login") {
                        LoginScreen(
                            onSignUp = { navController.navigate("terms") },
                            onLogin = { email, password ->
                                authManager.signInWithEmail(email, password)
                                    .onEach { result ->
                                        when (result) {
                                            is AuthResponse.Success -> {
                                                lifecycleScope.launch {
                                                    val step = authManager.getOnboardingStep()
                                                    Log.d("Login", "Valeur onboarding_step = '$step'")

                                                    when (step) {
                                                        "complete" -> {
                                                            navController.navigate("home") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "connection_goals" -> {
                                                            navController.navigate("selectConnectionGoals") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "interests" -> {
                                                            navController.navigate("selectInterests") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "bio" -> {
                                                            navController.navigate("writeBio") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "interest_languages" -> {
                                                            navController.navigate("selectInterestLanguages") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "native_language" -> {
                                                            navController.navigate("selectNativeLanguage") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "nationality" -> {
                                                            navController.navigate("selectNationality") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        "photo" -> {
                                                            navController.navigate("uploadPhoto") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                        else -> {
                                                            navController.navigate("profile") {
                                                                popUpTo("welcome") { inclusive = true }
                                                            }
                                                        }
                                                    }
                                                }
                                                Toast.makeText(context, "Connexion réussie !", Toast.LENGTH_SHORT).show()
                                                Log.d("auth", "Email Login Success")
                                            }
                                            is AuthResponse.Error -> {
                                                Toast.makeText(context, "Erreur connexion: ${result.message}", Toast.LENGTH_LONG).show()
                                                Log.e("auth", "Email Login Failed: ${result.message}")
                                            }
                                        }
                                    }
                                    .launchIn(coroutineScope)
                            },
                            onFacebookClick = {
                                Toast.makeText(context, "Facebook Login non implémenté.", Toast.LENGTH_SHORT).show()
                            },
                            onGoogleClick = {
                                val signInIntent = googleSignInClient.signInIntent
                                googleSignInLauncher.launch(signInIntent)
                            }
                        )
                    }
                    composable("profile") {
                        ProfileScreen(
                            onProfileComplete = { name, gender, birthday ->
                                authManager.updateProfile(name, gender, birthday)
                                    .onEach { result ->
                                        when (result) {
                                            is AuthResponse.Success -> {
                                Toast.makeText(context, "Profil complété avec succès !", Toast.LENGTH_SHORT).show()
                                                Log.d("auth", "Profile saved to database: $name, $gender, $birthday")
                                                navController.navigate("uploadPhoto") {
                                                    popUpTo("profile") { inclusive = true }
                                                }
                                            }
                                            is AuthResponse.Error -> {
                                                Toast.makeText(context, "Erreur sauvegarde: ${result.message}", Toast.LENGTH_LONG).show()
                                                Log.e("auth", "Profile save error: ${result.message}")
                                            }
                                        }
                                    }
                                    .launchIn(lifecycleScope)
                            }
                        )
                    }
                    composable("home") {
                        MainScreen(navController = navController, authManager = authManager)
                    }
                    composable("editProfile") {
                        ProfileScreen(
                            userProfile = UserProfile(
                                name = "Othmane",
                                age = 21,
                                distance = "3 km away",
                                aboutMe = "love ur self 💖",
                                interests = listOf("Soccer", "Basketball", "Baseball", "Golf", "Tennis"),
                                education = "University of Arts",
                                profession = "Creative Director"
                            ),
                            authManager = authManager,
                            navController = navController
                        )
                    }
                    composable("viewPhotos") {
                        ViewPhotosScreen(
                            authManager = authManager,
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("uploadPhoto") {
                        UploadPhotoScreen(
                            authManager = authManager,
                            onPhotoUploaded = {
                                navController.navigate("selectNationality") {
                                    popUpTo("uploadPhoto") { inclusive = true }
                                }
                            }
                        )
                    }
                    composable("selectNationality") {
                        SelectNationalityScreen(
                            authManager = authManager,
                            onNationalitySelected = { nationality ->
                                // Sauvegarder la nationalité et passer à la langue natale
                                navController.navigate("selectNativeLanguage")
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("selectNativeLanguage") {
                        SelectNativeLanguageScreen(
                            authManager = authManager,
                            onLanguageSelected = { language ->
                                // Sauvegarder la langue natale et passer aux langues d'intérêt
                                navController.navigate("selectInterestLanguages")
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("selectInterestLanguages") {
                        SelectInterestLanguagesScreen(
                            authManager = authManager,
                            onLanguagesSelected = { languages ->
                                // Sauvegarder les langues d'intérêt et passer aux objectifs
                                navController.navigate("selectConnectionGoals")
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("selectConnectionGoals") {
                        SelectConnectionGoalsScreen(
                            authManager = authManager,
                            onGoalSelected = { goal ->
                                // Sauvegarder l'objectif et passer aux intérêts
                                navController.navigate("selectInterests") {
                                    popUpTo("selectConnectionGoals") { inclusive = true }
                                }
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("selectInterests") {
                        SelectInterestsScreen(
                            authManager = authManager,
                            onInterestsSelected = { interests ->
                                // Sauvegarder les intérêts et passer à la bio
                                navController.navigate("writeBio") {
                                    popUpTo("selectInterests") { inclusive = true }
                                }
                            },
                            onSkip = {
                                // Sauvegarder l'étape même si on passe les intérêts
                                lifecycleScope.launch {
                                    try {
                                        authManager.updateOnboardingStep("bio")
                                            .collect { result ->
                                                when (result) {
                                                    is AuthResponse.Success -> {
                                                        navController.navigate("writeBio") {
                                                            popUpTo("selectInterests") { inclusive = true }
                                                        }
                                                    }
                                                    is AuthResponse.Error -> {
                                                        // En cas d'erreur, naviguer quand même
                                                        navController.navigate("writeBio") {
                                                            popUpTo("selectInterests") { inclusive = true }
                                                        }
                                                    }
                                                }
                                            }
                                    } catch (e: Exception) {
                                        // En cas d'erreur, naviguer quand même
                                        navController.navigate("writeBio") {
                                            popUpTo("selectInterests") { inclusive = true }
                                        }
                                    }
                                }
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("writeBio") {
                        WriteBioScreen(
                            authManager = authManager,
                            onBioSaved = { bio ->
                                // Sauvegarder la bio et terminer l'onboarding
                                navController.navigate("home") {
                                    popUpTo("writeBio") { inclusive = true }
                                }
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("editName") {
                        EditNameScreen(
                            authManager = authManager,
                            onNameSaved = { newName ->
                                navController.popBackStack()
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("editBio") {
                        EditBioScreen(
                            authManager = authManager,
                            onBioSaved = { newBio ->
                                navController.popBackStack()
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("editConnectionGoal") {
                        EditConnectionGoalScreen(
                            authManager = authManager,
                            onGoalSaved = { newGoal ->
                                navController.popBackStack()
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("editInterestLanguages") {
                        EditInterestLanguagesScreen(
                            authManager = authManager,
                            navController = navController,
                            onLanguagesSaved = { languages ->
                                navController.popBackStack()
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                    composable("selectLanguageLevel/{language}") { backStackEntry ->
                        val language = backStackEntry.arguments?.getString("language") ?: ""
                        SelectLanguageLevelScreen(
                            language = language,
                            onLevelSelected = { selectedLanguage, level ->
                                // Retourner à l'écran des langues avec la nouvelle langue
                                navController.popBackStack()
                            },
                            onBack = { navController.popBackStack() }
                        )
                    }
                }
            }
        }
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account = completedTask.getResult(ApiException::class.java)
            val idToken = account.idToken
            Log.d("GoogleSignIn", "handleSignInResult: idToken = $idToken")
            Log.d("GoogleSignIn", "handleSignInResult: account = $account")

            if (idToken != null) {
                Log.d("GoogleSignIn", "handleSignInResult: Début connexion avec idToken")
                authManager.signInWithGoogleIdToken(idToken)
                    .onEach { result ->
                        Log.d("GoogleSignIn", "handleSignInResult: Résultat connexion = $result")
                        when (result) {
                            is AuthResponse.Success -> {
                                Log.d("GoogleSignIn", "handleSignInResult: Connexion réussie, appel onGoogleSignInSuccess")
                                onGoogleSignInSuccess?.invoke()
                                Toast.makeText(this, "Connexion Google réussie !", Toast.LENGTH_SHORT).show()
                            }
                            is AuthResponse.Error -> {
                                Log.e("GoogleSignIn", "handleSignInResult: Erreur connexion = ${result.message}")
                                Toast.makeText(this, "Erreur connexion Google: ${result.message}", Toast.LENGTH_LONG).show()
                            }
                        }
                    }
                    .launchIn(lifecycleScope)
            } else {
                Log.e("GoogleSignIn", "handleSignInResult: idToken est null")
                Toast.makeText(this, "Erreur: idToken null", Toast.LENGTH_LONG).show()
            }
        } catch (e: ApiException) {
            Log.e("GoogleSignIn", "handleSignInResult: Exception = ${e.localizedMessage}")
            Toast.makeText(this, "Erreur Google Sign-In: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
        }
    }
}

@Composable
fun WelcomeScreen(onSignIn: () -> Unit, onSignUp: () -> Unit, onGoogleClick: (() -> Unit)? = null) {
    val images = listOf(
        R.drawable.welcome_bg1,
        R.drawable.welcome_bg2,
        R.drawable.welcome_bg3
    )
    val subTexts = listOf(
        "Find your Azelle",
        "Find on Azelle\nFriends, love, soulmates",
        "Video, Voice and chat\nEndless conversations"
    )
    var currentIndex by remember { mutableStateOf(0) }
    
    LaunchedEffect(Unit) {
        while (true) {
            delay(3000)
            currentIndex = (currentIndex + 1) % images.size
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = images[currentIndex]),
            contentDescription = "Background Image",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.35f))
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 32.dp, vertical = 48.dp),
            verticalArrangement = Arrangement.Bottom,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = "Azelle",
                        fontSize = 48.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = subTexts[currentIndex],
                        fontSize = 20.sp,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
            }
            Spacer(modifier = Modifier.height(32.dp))
            Button(
                onClick = onSignUp,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFD81B60)),
                shape = RoundedCornerShape(50),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Text("Get Started", fontSize = 20.sp)
            }
            Spacer(modifier = Modifier.height(16.dp))
            OutlinedButton(
                onClick = onSignIn,
                border = BorderStroke(2.dp, Color.White),
                shape = RoundedCornerShape(50),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Text("Log in", fontSize = 20.sp, color = Color.White)
            }
        }
    }
}

@Composable
fun SocialMediaButton(
    text: String,
    iconId: Int,
    onClick: () -> Unit,
    tint: Color = Color.Unspecified
) {
    OutlinedButton(
        onClick = onClick,
        border = BorderStroke(1.dp, Color(0xFFE0E0E0)),
        shape = RoundedCornerShape(8.dp),
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                painter = painterResource(id = iconId),
                contentDescription = text,
                tint = tint,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = text,
                color = Color(0xFF1A1B1E),
                fontSize = 18.sp
            )
        }
    }
}

@Composable
fun SignUpScreen(onSignIn: () -> Unit, onSignUp: (String, String) -> Unit, onFacebookClick: () -> Unit, onGoogleClick: () -> Unit) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var showEmailFields by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(48.dp))
        Text(
            text = "Azelle",
            fontSize = 40.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1A1B1E)
        )
        Spacer(modifier = Modifier.height(24.dp))
        Text(
            text = "Sign up",
            fontSize = 32.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1A1B1E)
        )
        Spacer(modifier = Modifier.height(32.dp))
        
        if (!showEmailFields) {
            Button(
                onClick = { showEmailFields = true },
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1A1B1E)),
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_email),
                        contentDescription = "Email",
                        tint = Color.White
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        "Continue with Email",
                        color = Color.White,
                        fontSize = 18.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SocialMediaButton(
                text = "Continue with Facebook",
                iconId = R.drawable.ic_facebook,
                onClick = onFacebookClick,
                tint = Color(0xFF1877F2)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SocialMediaButton(
                text = "Continue with Google",
                iconId = R.drawable.ic_google,
                onClick = onGoogleClick
            )
        } else {
            OutlinedTextField(
                value = email,
                onValueChange = { email = it },
                label = { Text("Email address") },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text("Password") },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp),
                visualTransformation = PasswordVisualTransformation()
            )
            Spacer(modifier = Modifier.height(24.dp))
            Button(
                onClick = { onSignUp(email, password) },
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1A1B1E)),
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Text("Sign up", fontSize = 18.sp)
            }
            Spacer(modifier = Modifier.height(16.dp))
            TextButton(
                onClick = { showEmailFields = false }
            ) {
                Text(
                    text = "Back to options",
                    color = Color(0xFF1A1B1E),
                    fontSize = 16.sp
                )
            }
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        Row(
            modifier = Modifier
                .padding(bottom = 24.dp)
                .align(Alignment.CenterHorizontally),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Already have an account? ",
                color = Color(0xFF1A1B1E),
                fontSize = 16.sp
            )
            TextButton(
                onClick = onSignIn,
                contentPadding = PaddingValues(0.dp),
                modifier = Modifier.defaultMinSize(minWidth = 1.dp, minHeight = 1.dp)
            ) {
                Text(
                    text = "Log in",
                    color = Color(0xFF1A1B1E),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun LoginScreen(
    onSignUp: () -> Unit,
    onLogin: (String, String) -> Unit,
    onFacebookClick: () -> Unit,
    onGoogleClick: () -> Unit
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(48.dp))
        Text(
            text = "Azelle",
            fontSize = 40.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1A1B1E)
        )
        Spacer(modifier = Modifier.height(24.dp))
        Text(
            text = "Welcome",
            fontSize = 32.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1A1B1E)
        )
        Spacer(modifier = Modifier.height(32.dp))
        
        SocialMediaButton(
            text = "Continue with Facebook",
            iconId = R.drawable.ic_facebook,
            onClick = onFacebookClick,
            tint = Color(0xFF1877F2)
        )
        Spacer(modifier = Modifier.height(16.dp))
        SocialMediaButton(
            text = "Continue with Google",
            iconId = R.drawable.ic_google,
            onClick = onGoogleClick
        )
        Spacer(modifier = Modifier.height(24.dp))
        Text(
            text = "or",
            color = Color(0xFF666666),
            fontSize = 16.sp
        )
        Spacer(modifier = Modifier.height(24.dp))
        
        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email address") },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Password") },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            visualTransformation = PasswordVisualTransformation()
        )
        Spacer(modifier = Modifier.height(24.dp))
        Button(
            onClick = { onLogin(email, password) },
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1A1B1E)),
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
        ) {
            Text("Sign in", fontSize = 18.sp)
        }
        Spacer(modifier = Modifier.weight(1f))
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(bottom = 24.dp)
        ) {
            Text(
                text = "Don't have an account?",
                color = Color(0xFF1A1B1E),
                fontSize = 16.sp
            )
            TextButton(onClick = onSignUp) {
                Text(
                    text = "Sign up",
                    color = Color(0xFF1A1B1E),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun TermsScreen(onBack: () -> Unit, onNext: () -> Unit) {
    var allTerms by remember { mutableStateOf(false) }
    var termsOfService by remember { mutableStateOf(false) }
    var privacyPolicy by remember { mutableStateOf(false) }
    var locationTerms by remember { mutableStateOf(false) }
    var over18 by remember { mutableStateOf(false) }
    var eventNotif by remember { mutableStateOf(false) }
    var nightNotif by remember { mutableStateOf(false) }

    fun updateAllTerms(checked: Boolean) {
        allTerms = checked
        termsOfService = checked
        privacyPolicy = checked
        locationTerms = checked
        over18 = checked
        eventNotif = checked
        nightNotif = checked
    }
    val canProceed = termsOfService && privacyPolicy && locationTerms && over18

    val scrollState = rememberScrollState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF6F6F8))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(scrollState)
                    .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(32.dp))
                Text("Hi!", fontSize = 32.sp, fontWeight = FontWeight.Bold, color = Color(0xFFD81B60))
                Text("First time in Azelle?", fontSize = 24.sp, fontWeight = FontWeight.Bold, color = Color(0xFF22223B))
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    "Please agree to the Terms for a fuller service experience.",
                    fontSize = 15.sp,
                    color = Color(0xFF6C6C80),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(24.dp))
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(18.dp),
                    elevation = CardDefaults.cardElevation(6.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White)
                ) {
                    Column(modifier = Modifier.padding(18.dp)) {
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = allTerms,
                                onCheckedChange = { updateAllTerms(!allTerms) },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text("Agree to all terms", fontWeight = FontWeight.Bold, fontSize = 17.sp)
                        }
                        Divider(modifier = Modifier.padding(vertical = 8.dp), color = Color(0xFFE0E0E0))
                        
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = termsOfService,
                                onCheckedChange = {
                                    termsOfService = it
                                    allTerms = termsOfService && privacyPolicy && locationTerms && over18 && eventNotif && nightNotif
                                },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text(
                                "(Required) Terms of Service",
                                color = if (termsOfService) Color(0xFF22223B) else Color(0xFFD81B60),
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        }
                        
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 12.dp),
                            shape = RoundedCornerShape(14.dp),
                            elevation = CardDefaults.cardElevation(2.dp),
                            colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8))
                        ) {
                            Text(
                                "By agreeing to the Terms of Service, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions outlined in our Terms of Service document.",
                                fontSize = 13.sp,
                                color = Color(0xFF6C6C80),
                                modifier = Modifier.padding(16.dp)
                            )
                        }
                        
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = privacyPolicy,
                                onCheckedChange = {
                                    privacyPolicy = it
                                    allTerms = termsOfService && privacyPolicy && locationTerms && over18 && eventNotif && nightNotif
                                },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text(
                                "(Required) Privacy Policy",
                                color = if (privacyPolicy) Color(0xFF22223B) else Color(0xFFD81B60),
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        }
                        
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 12.dp),
                            shape = RoundedCornerShape(14.dp),
                            elevation = CardDefaults.cardElevation(2.dp),
                            colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8))
                        ) {
                            Text(
                                "By agreeing to the Privacy Policy, you consent to the collection, use, and sharing of your personal information as described in our Privacy Policy document.",
                                fontSize = 13.sp,
                                color = Color(0xFF6C6C80),
                                modifier = Modifier.padding(16.dp)
                            )
                        }
                        
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = locationTerms,
                                onCheckedChange = {
                                    locationTerms = it
                                    allTerms = termsOfService && privacyPolicy && locationTerms && over18 && eventNotif && nightNotif
                                },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text(
                                "(Required) Terms and Conditions of Location-Based Services",
                                color = if (locationTerms) Color(0xFF22223B) else Color(0xFFD81B60),
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        }
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = over18,
                                onCheckedChange = {
                                    over18 = it
                                    allTerms = termsOfService && privacyPolicy && locationTerms && over18 && eventNotif && nightNotif
                                },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text(
                                "(Required) Over 18 years of age.",
                                color = if (over18) Color(0xFF22223B) else Color(0xFFD81B60),
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        }
                        
                        Divider(modifier = Modifier.padding(vertical = 8.dp), color = Color(0xFFE0E0E0))
                        
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = eventNotif,
                                onCheckedChange = {
                                    eventNotif = it
                                    allTerms = termsOfService && privacyPolicy && locationTerms && over18 && eventNotif && nightNotif
                                },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text(
                                "Event notifications",
                                color = if (eventNotif) Color(0xFF22223B) else Color(0xFF6C6C80),
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        }
                        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()) {
                            Checkbox(
                                checked = nightNotif,
                                onCheckedChange = {
                                    nightNotif = it
                                    allTerms = termsOfService && privacyPolicy && locationTerms && over18 && eventNotif && nightNotif
                                },
                                colors = CheckboxDefaults.colors(checkedColor = Color(0xFFD81B60))
                            )
                            Text(
                                "Night notifications",
                                color = if (nightNotif) Color(0xFF22223B) else Color(0xFF6C6C80),
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        }
                    }
                }
            }
            
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    OutlinedButton(
                        onClick = onBack,
                        modifier = Modifier
                            .weight(1f)
                            .height(52.dp),
                        shape = RoundedCornerShape(50)
                    ) {
                        Text("Back", fontSize = 18.sp)
                    }
                    Spacer(modifier = Modifier.width(16.dp))
                    Button(
                        onClick = onNext,
                        enabled = canProceed,
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFD81B60)),
                        modifier = Modifier
                            .weight(1f)
                            .height(52.dp),
                        shape = RoundedCornerShape(50)
                    ) {
                        Text("Next", fontSize = 18.sp)
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
fun ProfileScreen(onProfileComplete: (String, String, String) -> Unit) {
    var name by remember { mutableStateOf("") }
    var selectedGender by remember { mutableStateOf("") }
    var day by remember { mutableStateOf("") }
    var month by remember { mutableStateOf("") }
    var year by remember { mutableStateOf("") }
    
    val genders = listOf("Homme", "Femme", "Non-binaire", "Préfère ne pas dire")
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 24.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(48.dp))
        
        Text(
            text = "Complétez votre profil",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1A1B1E)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Ces informations nous aident à personnaliser votre expérience",
            fontSize = 16.sp,
            color = Color(0xFF666666),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Champ Nom
        OutlinedTextField(
            value = name,
            onValueChange = { name = it },
            label = { Text("Nom complet") },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Sélection du genre
        Text(
            text = "Genre",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF1A1B1E),
            modifier = Modifier.align(Alignment.Start)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        genders.forEach { gender ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedGender == gender,
                    onClick = { selectedGender = gender },
                    colors = RadioButtonDefaults.colors(selectedColor = Color(0xFFD81B60))
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = gender,
                    fontSize = 16.sp,
                    color = Color(0xFF1A1B1E)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Date de naissance
        Text(
            text = "Date de naissance",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF1A1B1E),
            modifier = Modifier.align(Alignment.Start)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = day,
                onValueChange = { if (it.length <= 2) day = it },
                label = { Text("Jour") },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(8.dp),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number
                )
            )
            OutlinedTextField(
                value = month,
                onValueChange = { if (it.length <= 2) month = it },
                label = { Text("Mois") },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(8.dp),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number
                )
            )
            OutlinedTextField(
                value = year,
                onValueChange = { if (it.length <= 4) year = it },
                label = { Text("Année") },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(8.dp),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number
                )
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Bouton Continuer
        Button(
            onClick = { 
                val birthday = String.format("%04d-%02d-%02d", year.toIntOrNull() ?: 0, month.toIntOrNull() ?: 0, day.toIntOrNull() ?: 0)
                if (name.isNotBlank() && selectedGender.isNotBlank() && day.isNotBlank() && month.isNotBlank() && year.isNotBlank()) {
                    onProfileComplete(name, selectedGender, birthday)
                }
            },
            enabled = name.isNotBlank() && selectedGender.isNotBlank() && day.isNotBlank() && month.isNotBlank() && year.isNotBlank(),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFD81B60)),
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
        ) {
            Text(
                "Continuer",
                fontSize = 18.sp,
                color = Color.White
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Composable
fun HomeScreen(onViewPhotos: () -> Unit = {}) {
    var userProfile by remember { mutableStateOf<UserProfile?>(null) }
    var currentPhotoIndex by remember { mutableStateOf(0) }
    var photoUrls by remember { mutableStateOf<List<String>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }

    // Charger le profil utilisateur
    LaunchedEffect(Unit) {
        // TODO: Charger les données du profil depuis AuthManager
        isLoading = false
        // Données temporaires pour la démo
        userProfile = UserProfile(
            name = "Lauren",
            age = 24,
            distance = "3 km away",
            aboutMe = "Looking for someone to watch movies with",
            interests = listOf("Movies", "Galleries", "Cycling"),
            education = "University of Arts",
            profession = "Creative Director"
        )
    }

    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = Color(0xFFE91E63))
        }
    } else {
        ProfileScreen(
            userProfile = userProfile,
            photoUrls = photoUrls,
            currentPhotoIndex = currentPhotoIndex,
            onPhotoChange = { currentPhotoIndex = it },
            onViewPhotos = onViewPhotos
        )
    }
}

// Data class pour le profil utilisateur
data class UserProfile(
    val name: String,
    val age: Int,
    val distance: String,
    val aboutMe: String,
    val interests: List<String>,
    val education: String,
    val profession: String,
    val photoUrl: String = ""
)

// Écran principal avec navigation entre les pages
@Composable
fun MainScreen(navController: NavHostController, authManager: AuthManager) {
    var currentRoute by remember { mutableStateOf("swipe") }
    val lightGray = Color(0xFFF5F5F5)

    Box(modifier = Modifier.fillMaxSize()) {
        // Contenu principal selon la route actuelle
        when (currentRoute) {
            "swipe" -> DiscoverScreen()
            "like" -> LikeScreen()
            "discover" -> DiscoverPageScreen()
            "chat" -> ChatScreen()
            "profile" -> ProfileMainScreen()
        }

        // Coins du bas - Liés à la barre de navigation (FIXES)
        // Coin bas gauche - FIXE avec la barre de navigation
        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .size(16.dp)
                .offset(x = 1.dp, y = (-68).dp) // Descendu de 12dp (était -80dp)
                .background(
                    lightGray,
                    RoundedCornerShape(topEnd = 16.dp)
                )
                .zIndex(15f)
        )

        // Coin bas droit - FIXE avec la barre de navigation
        Box(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .size(16.dp)
                .offset(x = (-1).dp, y = (-68).dp) // Descendu de 12dp (était -80dp)
                .background(
                    lightGray,
                    RoundedCornerShape(topStart = 16.dp)
                )
                .zIndex(15f)
        )

        // Barre de navigation en bas
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .zIndex(10f)
        ) {
            BottomNavigationBar(
                currentRoute = currentRoute,
                onRouteChange = { route -> currentRoute = route }
            )
        }
    }
}

// Page Like - Écran vide pour tester les icônes
@Composable
fun LikeScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = "Like Page",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1A1B1E)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Page vide pour tester l'icône Like",
                fontSize = 16.sp,
                color = Color.Gray
            )
        }
    }
}

// Page Discover - Écran vide pour tester les icônes
@Composable
fun DiscoverPageScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = "Discover Page",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1A1B1E)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Page vide pour tester l'icône Discover",
                fontSize = 16.sp,
                color = Color.Gray
            )
        }
    }
}

// Page Chat - Écran vide pour tester les icônes
@Composable
fun ChatScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = "Chat Page",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1A1B1E)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Page vide pour tester l'icône Chat",
                fontSize = 16.sp,
                color = Color.Gray
            )
        }
    }
}

// Page Profile - Écran vide pour tester les icônes
@Composable
fun ProfileMainScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = "Profile Page",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1A1B1E)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Page vide pour tester l'icône Profile",
                fontSize = 16.sp,
                color = Color.Gray
            )
        }
    }
}

// Barre de navigation en bas avec gestion des états
@Composable
fun BottomNavigationBar(
    currentRoute: String,
    onRouteChange: (String) -> Unit
) {
    val lightGray = Color(0xFFF5F5F5)

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(75.dp) // Augmenté de 15dp (était 60dp)
            .background(
                lightGray,
                shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
            )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.Center)
        ) {
            // 1. SWIPE - Position absolue isolée
            Box(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .offset(x = 21.dp, y = (-2).dp) // Position absolue fixe
            ) {
                NavigationIcon(
                    iconRes = if (currentRoute == "swipe") R.drawable.icon_swipe_black else R.drawable.icon_swipe_light,
                    text = "Swipe",
                    isSelected = currentRoute == "swipe",
                    onClick = { }, // Effet cliquable supprimé
                    modifier = Modifier
                )
            }

            // EFFET CLIQUABLE MANUEL POUR SWIPE - Largeur ultra-maximisée
            Box(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .offset(x = 4.dp, y = (-8).dp) // x-3dp pour recentrer la nouvelle largeur
                    .size(width = 69.dp, height = 49.dp) // Largeur +6dp (3+3), hauteur fixe
                    .clickable { onRouteChange("swipe") }
            )

            // 2. LIKE - Position absolue isolée
            Box(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .offset(x = 91.dp, y = (-2).dp) // Déplacé de 7dp supplémentaires vers la gauche (était 98dp)
            ) {
                NavigationIcon(
                    iconRes = if (currentRoute == "like") R.drawable.icon_like_black else R.drawable.icon_like_light,
                    text = "Liked You",
                    isSelected = currentRoute == "like",
                    onClick = { onRouteChange("like") },
                    modifier = Modifier
                )
            }

            // EFFET CLIQUABLE MANUEL POUR LIKE - Mêmes dimensions que Swipe
            Box(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .offset(x = 91.dp, y = (-8).dp) // Position de départ (même x que Like, même y que Swipe)
                    .size(width = 69.dp, height = 49.dp) // Mêmes dimensions que Swipe
                    .clickable { onRouteChange("like") }
            )

            // 3. DISCOVER - Position absolue isolée
            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .offset(x = (-3).dp, y = 0.5.dp) // Déplacé de 5dp vers la droite (était -10.4dp)
            ) {
                NavigationIcon(
                    iconRes = if (currentRoute == "discover") R.drawable.icon_discover_black else R.drawable.icon_discover_light,
                    text = "Discover",
                    isSelected = currentRoute == "discover",
                    onClick = { onRouteChange("discover") },
                    modifier = Modifier
                )
            }

            // 4. CHAT - Position absolue isolée
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .offset(x = (-102).dp, y = 0.8.dp) // Déplacé de 8dp vers la droite (était -110dp)
            ) {
                NavigationIcon(
                    iconRes = if (currentRoute == "chat") R.drawable.icon_chat_black else R.drawable.icon_chat_light,
                    text = "Chats",
                    isSelected = currentRoute == "chat",
                    onClick = { onRouteChange("chat") },
                    modifier = Modifier
                )
            }

            // 5. PROFILE - Position absolue isolée
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .offset(x = (-21).dp, y = (-2.1).dp) // Position absolue fixe
            ) {
                NavigationIcon(
                    iconRes = if (currentRoute == "profile") R.drawable.icon_profil_black else R.drawable.icon_profil_light,
                    text = "Profile",
                    isSelected = currentRoute == "profile",
                    onClick = { onRouteChange("profile") },
                    modifier = Modifier
                )
            }
        }
    }
}

// Composant pour chaque icône de navigation
@Composable
fun NavigationIcon(
    iconRes: Int,
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier // Effet cliquable supprimé
    ) {
        Image(
            painter = painterResource(id = iconRes),
            contentDescription = text,
            modifier = Modifier
                .size(
                    when (text) {
                        "Swipe" -> if (isSelected) 38.8.dp else 33.dp // Réduit de 4dp (était 39dp)
                        "Liked You" -> 28.dp
                        "Discover" -> if (isSelected) 33.3.dp else 25.dp // +7dp supplémentaires pour l'icône noire (était 32dp)
                        "Chats" -> 25.dp
                        "Profile" -> 31.dp
                        else -> 28.dp
                    }
                )
                .offset(
                    x = when (text) {
                        "Swipe" -> if (isSelected) (-2).dp else 0.dp // Déplacé de 4dp vers la gauche quand noir
                        else -> 0.dp
                    },
                    y = when (text) {
                        "Swipe" -> if (isSelected) (5.4).dp else 6.dp // Descendu de 1.4dp supplémentaires (4dp + 1.4dp = 5.4dp)
                        "Liked You" -> 7.5.dp
                        "Discover" -> 5.5.dp
                        "Chats" -> 5.8.dp
                        "Profile" -> 6.9.dp
                        else -> 5.dp
                    }
                )
        )
        Spacer(modifier = Modifier.height(2.dp))
        Text(
            text = text,
            fontSize = 10.sp,
            color = if (isSelected) Color.Black else Color.Gray,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            modifier = Modifier.offset(
                x = when (text) {
                    "Swipe" -> if (isSelected) (-2.5).dp else 0.dp // Texte Swipe noir déplacé de 3dp supplémentaires vers la gauche (était -3dp)
                    else -> 0.dp
                },
                y = when (text) {
                    "Swipe" -> if (isSelected) (-7.42).dp else (-2.82).dp // Descendu de 1.4dp (-8.82dp + 1.4dp = -7.42dp)
                    "Liked You" -> 0.5.dp
                    "Discover" -> if (isSelected) (-4.8).dp else 0.7.dp // Ajusté pour compenser la taille d'icône plus grande
                    "Chats" -> 0.8.dp
                    "Profile" -> 0.9.dp
                    else -> 0.dp
                }
            )
        )
    }
}

// Écran des matches
@Composable
fun MatchesScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            "❤️",
            fontSize = 64.sp
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            "Vos Matches",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            "Aucun match pour le moment",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
    }
}

// Écran des conversations
@Composable
fun ConversationsScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            "💬",
            fontSize = 64.sp
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            "Conversations",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            "Aucune conversation pour le moment",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
    }
}

// Nouvelle page Mon Profil (dashboard style)
@Composable
fun MyProfileScreen(
    onNavigateToEditProfile: () -> Unit,
    authManager: AuthManager? = null
) {
    // État pour la première photo de l'utilisateur
    var firstPhotoUrl by remember { mutableStateOf<String?>(null) }
    var isLoadingPhoto by remember { mutableStateOf(true) }

    // État pour les données du profil
    var userName by remember { mutableStateOf("Utilisateur") }
    var userNationality by remember { mutableStateOf("") }
    var userBirthday by remember { mutableStateOf("") }
    var isLoadingProfile by remember { mutableStateOf(true) }

    // Charger les données du profil et la première photo
    LaunchedEffect(Unit) {
        authManager?.let { manager ->
            try {
                // Charger les données du profil séparément
                Log.d("MyProfileScreen", "Début chargement profil...")
                userName = manager.getUserName()
                userNationality = manager.getUserNationality()
                userBirthday = manager.getUserBirthday()
                Log.d("MyProfileScreen", "Données chargées: nom=$userName, nationalité=$userNationality, anniversaire=$userBirthday")
                isLoadingProfile = false

                // Charger la première photo
                val photoUrls = manager.getPublicPhotoUrls()
                firstPhotoUrl = photoUrls.firstOrNull()
                Log.d("MyProfileScreen", "Photo chargée: $firstPhotoUrl")
                isLoadingPhoto = false
            } catch (e: Exception) {
                Log.e("MyProfileScreen", "Erreur chargement", e)
                isLoadingProfile = false
                isLoadingPhoto = false
            }
        } ?: run {
            Log.e("MyProfileScreen", "AuthManager est null")
            isLoadingProfile = false
            isLoadingPhoto = false
        }
    }

    // Fonction pour obtenir le drapeau selon la nationalité
    fun getFlagForNationality(nationality: String?): String {
        return when (nationality?.lowercase()) {
            "maroc", "morocco" -> "🇲🇦"
            "france" -> "🇫🇷"
            "espagne", "spain" -> "🇪🇸"
            "italie", "italy" -> "🇮🇹"
            "allemagne", "germany" -> "🇩🇪"
            "royaume-uni", "uk", "united kingdom" -> "🇬🇧"
            "états-unis", "usa", "united states" -> "🇺🇸"
            "canada" -> "🇨🇦"
            "belgique", "belgium" -> "🇧🇪"
            "suisse", "switzerland" -> "🇨🇭"
            "portugal" -> "🇵🇹"
            "pays-bas", "netherlands" -> "🇳🇱"
            "algérie", "algeria" -> "🇩🇿"
            "tunisie", "tunisia" -> "🇹🇳"
            else -> "🌍" // Drapeau par défaut
        }
    }

    // Fonction pour calculer l'âge à partir de la date de naissance
    fun calculateAge(birthday: String): String {
        return try {
            val parts = birthday.split("-")
            if (parts.size == 3) {
                val year = parts[0].toInt()
                val currentYear = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
                (currentYear - year).toString()
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }



    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        item {
        // Header avec "My" seulement
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "My",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Section photo de profil avec pin d'édition
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                // Photo de profil cliquable
                Card(
                    modifier = Modifier
                        .size(120.dp)
                        .clickable { onNavigateToEditProfile() },
                    shape = CircleShape,
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    if (isLoadingPhoto) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    brush = Brush.radialGradient(
                                        colors = listOf(
                                            Color(0xFF4CAF50),
                                            Color(0xFF2196F3)
                                        )
                                    )
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                color = Color.White,
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    } else if (firstPhotoUrl != null) {
                        AsyncImage(
                            model = firstPhotoUrl,
                            contentDescription = "Photo de profil",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                    } else {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    brush = Brush.radialGradient(
                                        colors = listOf(
                                            Color(0xFF4CAF50),
                                            Color(0xFF2196F3)
                                        )
                                    )
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                "👤",
                                fontSize = 48.sp,
                                color = Color.White
                            )
                        }
                    }
                }

                // Bordure rose autour de la photo
                Box(
                    modifier = Modifier
                        .size(130.dp)
                        .border(
                            width = 3.dp,
                            color = Color(0xFFE91E63),
                            shape = CircleShape
                        )
                )

                // Pin d'édition en haut à droite
                Box(
                    modifier = Modifier
                        .offset(x = 35.dp, y = (-35).dp)
                        .size(32.dp)
                        .background(
                            color = Color.White,
                            shape = CircleShape
                        )
                        .border(
                            width = 2.dp,
                            color = Color(0xFFE91E63),
                            shape = CircleShape
                        )
                        .clickable { onNavigateToEditProfile() },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "✏️",
                        fontSize = 16.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Nom et âge avec drapeau
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Drapeau basé sur la nationalité
                Log.d("MyProfileScreen", "Nationalité récupérée: '$userNationality'")
                Text(getFlagForNationality(userNationality), fontSize = 24.sp)

                // Nom et âge basés sur les données du profil
                val age = if (userBirthday.isNotEmpty()) calculateAge(userBirthday) else ""

                Log.d("MyProfileScreen", "Nom: '$userName', Birthday: '$userBirthday', Age: '$age'")

                Text(
                    text = if (age.isNotEmpty()) "$userName $age" else userName,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Section bleue des visiteurs de profil
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF6366F1) // Bleu violet
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Avatars des visiteurs
                Row(
                    horizontalArrangement = Arrangement.spacedBy((-8).dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Avatar principal au centre
                    Card(
                        modifier = Modifier.size(60.dp),
                        shape = CircleShape,
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color.White),
                            contentAlignment = Alignment.Center
                        ) {
                            Text("👩", fontSize = 24.sp)
                        }
                    }

                    // Petits avatars autour
                    repeat(4) { index ->
                        Card(
                            modifier = Modifier.size(40.dp),
                            shape = CircleShape,
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(Color.White.copy(alpha = 0.9f)),
                                contentAlignment = Alignment.Center
                            ) {
                                val emojis = listOf("👨", "👩", "🧑", "👱")
                                Text(emojis[index], fontSize = 16.sp)
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Texte explicatif
                Text(
                    text = "Check out who visited\nmy profile now!",
                    color = Color.White,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Bouton "View Profile Visitors"
                Button(
                    onClick = { /* TODO: Voir les visiteurs */ },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(25.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "View Profile Visitors",
                        color = Color(0xFF6366F1),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Menu des options
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            val menuItems = listOf(
                "Event" to "🎉",
                "Notice" to "📢",
                "FAQ" to "❓",
                "Settings" to "⚙️"
            )

            menuItems.forEachIndexed { index, (title, icon) ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 6.dp)
                        .clickable { /* TODO: Navigation vers $title */ },
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Text(icon, fontSize = 20.sp)
                            Text(
                                text = title,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color.Black
                            )
                        }

                        Text(
                            text = "›",
                            fontSize = 20.sp,
                            color = Color.Gray,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(100.dp)) // Plus d'espace pour éviter que la bottom bar cache le contenu
        }
    }
}

// Écran de découverte - Scroll simple dans la zone de l'image
@Composable
fun DiscoverScreen() {
    val scrollState = rememberScrollState()
    val lightGray = Color(0xFFF5F5F5) // Gris clair comme dans l'exemple Bumble

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // BARRE DU HAUT FIXE - Blanc avec logo (réduite)
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp) // Réduit de 80dp à 60dp
                .background(Color.White)
                .zIndex(10f)
        ) {
            Text(
                text = "azelle",
                fontSize = 60.sp,
                fontFamily = FontFamily(androidx.compose.ui.text.font.Font(R.font.island_moments_regular)),
                color = Color(0xFF1A1B1E),
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 16.dp)
            )
        }

        // EXTRÉMITÉS GRIS FIXES - Élargies pour couvrir les coins arrondis
        // Côté gauche - FIXE avec coins arrondis pour s'adapter à l'image
        Box(
            modifier = Modifier
                .align(Alignment.CenterStart)
                .width(8.dp) // Restauré à 8dp pour garder la largeur de l'image
                .fillMaxHeight()
                .padding(top = 70.dp, bottom = 75.dp) // Ajusté bottom de 10dp supplémentaires (était 65dp)
                .background(
                    lightGray,
                    shape = RoundedCornerShape(
                        topStart = 16.dp,    // Coin arrondi en haut à gauche
                        bottomStart = 20.dp, // Effet arrondi vers la barre du bas
                        topEnd = 0.dp,       // Pas d'arrondi côté intérieur
                        bottomEnd = 0.dp     // Pas d'arrondi côté intérieur
                    )
                )
                .zIndex(15f) // Au-dessus du contenu scrollable
        )

        // Côté droit - FIXE avec coins arrondis pour s'adapter à l'image
        Box(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .width(8.dp) // Restauré à 8dp pour garder la largeur de l'image
                .fillMaxHeight()
                .padding(top = 70.dp, bottom = 75.dp) // Ajusté bottom de 10dp supplémentaires (était 65dp)
                .background(
                    lightGray,
                    shape = RoundedCornerShape(
                        topEnd = 16.dp,      // Coin arrondi en haut à droite
                        bottomEnd = 20.dp,   // Effet arrondi vers la barre du bas
                        topStart = 0.dp,     // Pas d'arrondi côté intérieur
                        bottomStart = 0.dp   // Pas d'arrondi côté intérieur
                    )
                )
                .zIndex(15f) // Au-dessus du contenu scrollable
        )

        // ZONE SCROLLABLE SIMPLE - Dans la zone de l'image
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 70.dp, bottom = 75.dp) // Augmenté le bottom de 10dp supplémentaires (était 65dp)
                .verticalScroll(scrollState)
        ) {
            // ZONE DE L'IMAGE - Hauteur réduite de 10dp supplémentaires depuis le bas
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(679.dp) // Réduit de 10dp supplémentaires depuis le bas (était 689dp)
                    .padding(horizontal = 8.dp) // Restauré à 8dp pour garder la largeur de l'image
            ) {
                // FOND GRIS CLAIR pour remplir les coins arrondis
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            lightGray,
                            RoundedCornerShape(16.dp)
                        )
                )

                // IMAGE ANNA EXACTEMENT COMME AVANT - LARGEUR ORIGINALE
                Image(
                    painter = painterResource(id = R.drawable.anna_profile_picture),
                    contentDescription = "Anna profile picture",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .fillMaxWidth() // LARGEUR COMPLÈTE - pas 0.99f
                        .fillMaxHeight()
                        .clip(RoundedCornerShape(16.dp))
                )

                // Texte "Anna, 26" directement sur l'image - Déplacé vers le bas
                Text(
                    text = "Anna, 26",
                    fontSize = 28.sp,
                    fontFamily = FontFamily(androidx.compose.ui.text.font.Font(R.font.poller_one_regular)),
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .padding(start = 24.dp, bottom = 110.dp) // Réduit de 115dp à 110dp (5dp supplémentaires vers le bas)
                        .zIndex(15f)
                )

                // Icône coeur rouge - Déplacé vers le bas
                Image(
                    painter = painterResource(id = R.drawable.like_icon_rouge),
                    contentDescription = "Like icon",
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .size(100.dp)
                        .padding(16.dp)
                        .offset(x = 8.dp, y = (-10).dp) // Changé de -15dp à -10dp (5dp supplémentaires vers le bas)
                        .zIndex(100f)
                        .drawWithCache {
                            onDrawWithContent {
                                // Dessiner une ombre subtile derrière l'icône
                                drawCircle(
                                    Color.Black.copy(alpha = 0.5f),
                                    radius = size.width / 2,
                                    blendMode = BlendMode.DstOver
                                )
                                drawContent()
                            }
                        }
                        .shadow(elevation = 12.dp, shape = CircleShape)
                )

                // Icône étoile verte - Déplacé vers le bas
                Image(
                    painter = painterResource(id = R.drawable.icone_etoile_verte),
                    contentDescription = "Star icon",
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(100.dp)
                        .padding(16.dp)
                        .offset(x = (-8).dp, y = (-50).dp) // Changé de -55dp à -50dp (5dp supplémentaires vers le bas)
                        .zIndex(100f)
                        .drawWithCache {
                            onDrawWithContent {
                                // Dessiner une ombre subtile derrière l'icône
                                drawCircle(
                                    Color.Black.copy(alpha = 0.5f),
                                    radius = size.width / 2,
                                    blendMode = BlendMode.DstOver
                                )
                                drawContent()
                            }
                        }
                        .shadow(elevation = 12.dp, shape = CircleShape)
                )

                // FORMES ARRONDIES pour remplir les espaces blancs dans les coins
                // Coin haut gauche - SENS OPPOSÉ avec décalage
                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .size(16.dp)
                        .offset(x = (-7).dp, y = (-7).dp) // Décalé à -7dp vers la gauche et vers le haut
                        .background(
                            lightGray,
                            RoundedCornerShape(bottomEnd = 16.dp) // SENS OPPOSÉ
                        )
                )

                // Coin haut droit - SENS OPPOSÉ avec décalage
                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(16.dp)
                        .offset(x = 7.dp, y = (-7).dp) // Décalé vers la droite et vers le haut
                        .background(
                            lightGray,
                            RoundedCornerShape(bottomStart = 16.dp) // SENS OPPOSÉ
                        )
                )


            }

            // CONTENU DU PROFIL - Apparaît quand on scroll vers le bas
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp) // Même padding que l'image pour éviter les extrémités
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                        )
                        .padding(16.dp)
                ) {
                Text(
                    text = "About Anna",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Text(
                    text = "This is the profile information that appears when you scroll down in the image zone.",
                    fontSize = 16.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                // Plus de contenu du profil
                repeat(20) { index ->
                    Text(
                        text = "Profile detail ${index + 1}",
                        fontSize = 14.sp,
                        color = Color.Black,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }

                Spacer(modifier = Modifier.height(100.dp))
                }
            }
        }

        // COINS DU BAS - Liés à la barre de navigation (FIXES)
        // Coin bas gauche - FIXE avec la barre de navigation
        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .size(16.dp)
                .offset(x = 1.dp, y = (-50).dp) // Descendu à -50dp
                .background(
                    lightGray,
                    RoundedCornerShape(topEnd = 16.dp) // SENS OPPOSÉ
                )
                .zIndex(15f)
        )

        // Coin bas droit - FIXE avec la barre de navigation
        Box(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .size(16.dp)
                .offset(x = (-1).dp, y = (-50).dp) // Descendu à -50dp
                .background(
                    lightGray,
                    RoundedCornerShape(topStart = 16.dp) // SENS OPPOSÉ
                )
                .zIndex(15f)
        )


    }
}

@Composable
fun ProfileScreen(
    userProfile: UserProfile?,
    photoUrls: List<String> = emptyList(),
    currentPhotoIndex: Int = 0,
    onPhotoChange: (Int) -> Unit = {},
    onViewPhotos: () -> Unit = {},
    authManager: AuthManager? = null,
    navController: NavHostController? = null
) {
    // État pour les photos de l'utilisateur
    var userPhotoUrls by remember { mutableStateOf<List<String>>(emptyList()) }
    var isLoadingPhotos by remember { mutableStateOf(true) }

    // État pour les données du profil réel - CHARGEMENT OPTIMISÉ
    var userName by remember { mutableStateOf("") }
    var userNationality by remember { mutableStateOf("") }
    var userBirthday by remember { mutableStateOf("") }
    var userBio by remember { mutableStateOf("") }
    var userConnectionGoal by remember { mutableStateOf("") }
    var userNativeLanguage by remember { mutableStateOf("") }
    var userInterestLanguages by remember { mutableStateOf<List<Map<String, Any>>>(emptyList()) }
    var userInterests by remember { mutableStateOf<List<String>>(emptyList()) }
    var isLoadingProfile by remember { mutableStateOf(false) } // DÉMARRER DIRECTEMENT SANS LOADING

    // CHARGEMENT ULTRA RAPIDE - TOUTES LES DONNÉES EN PARALLÈLE
    LaunchedEffect(Unit) {
        authManager?.let { manager ->
            try {
                // CHARGEMENT EN PARALLÈLE POUR VITESSE MAXIMALE
                val deferredPhotos = async { manager.getPublicPhotoUrls() }
                val deferredName = async { manager.getUserName() }
                val deferredNationality = async { manager.getUserNationality() }
                val deferredBirthday = async { manager.getUserBirthday() }
                val deferredBio = async { manager.getUserBio() }
                val deferredGoal = async { manager.getUserConnectionGoal() }
                val deferredLanguage = async { manager.getUserNativeLanguage() }
                val deferredInterestLangs = async { manager.getUserInterestLanguages() }
                val deferredInterests = async { manager.getUserInterests() }

                // RÉCUPÉRATION INSTANTANÉE DE TOUTES LES DONNÉES
                userPhotoUrls = deferredPhotos.await()
                userName = deferredName.await()
                userNationality = deferredNationality.await()
                userBirthday = deferredBirthday.await()
                userBio = deferredBio.await()
                userConnectionGoal = deferredGoal.await()
                userNativeLanguage = deferredLanguage.await()
                userInterestLanguages = deferredInterestLangs.await()
                userInterests = deferredInterests.await()

                isLoadingPhotos = false

                Log.d("ProfileScreen", "TOUTES les données chargées en parallèle: nom=$userName, objectif=$userConnectionGoal")

            } catch (e: Exception) {
                Log.e("ProfileScreen", "Erreur chargement", e)
                isLoadingPhotos = false
            }
        } ?: run {
            isLoadingPhotos = false
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {


        // Interface d'édition de profil
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // Section Profile photo
            item {
                Text(
                    "Profile photo",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Grille de photos 2x3
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.height(300.dp)
                ) {
                    // Photo principale (plus grande) - première photo uploadée
                    item {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(140.dp),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            if (isLoadingPhotos) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color(0xFFF0F0F0)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(
                                        color = Color(0xFFE91E63),
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                            } else if (userPhotoUrls.isNotEmpty()) {
                                AsyncImage(
                                    model = userPhotoUrls[0],
                                    contentDescription = "Photo principale",
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Crop
                                )
                            } else {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color(0xFFF0F0F0)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text("📷", fontSize = 32.sp)
                                }
                            }
                        }
                    }

                    // Photos secondaires (5 emplacements)
                    items(5) { index ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(if (index == 0) 140.dp else 70.dp),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            val photoIndex = index + 1 // +1 car la première photo est déjà utilisée
                            if (isLoadingPhotos) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color(0xFFF0F0F0)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(
                                        color = Color(0xFFE91E63),
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            } else if (photoIndex < userPhotoUrls.size) {
                                AsyncImage(
                                    model = userPhotoUrls[photoIndex],
                                    contentDescription = "Photo $photoIndex",
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Crop
                                )
                            } else {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(Color(0xFFF0F0F0)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text("+", fontSize = 24.sp, color = Color.Gray)
                                }
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))
            }

            // Section Introduction
            item {
                Text(
                    "Introduction",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(12.dp))

                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            navController?.navigate("editBio")
                        },
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            if (userBio.isNotEmpty()) userBio else "Aucune bio ajoutée",
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                        Text(">", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))
            }

            // Section Interests
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        "Interests",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Black
                    )
                    Text(
                        "Important",
                        fontSize = 14.sp,
                        color = Color(0xFFE91E63),
                        fontWeight = FontWeight.Medium
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Affichage dynamique des intérêts
                if (userInterests.isNotEmpty()) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.height(120.dp)
                    ) {
                        items(userInterests.take(6)) { interest ->
                            Card(
                                colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F0F0)),
                                shape = RoundedCornerShape(20.dp)
                            ) {
                                Row(
                                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(getEmojiForInterest(interest), fontSize = 16.sp)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(interest, fontSize = 14.sp, color = Color.Black)
                                }
                            }
                        }
                    }
                } else {
                    Text(
                        "Aucun intérêt ajouté",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(16.dp)
                    )
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    Text(">", fontSize = 18.sp, color = Color.Gray)
                }

                Spacer(modifier = Modifier.height(32.dp))
            }

            // Section What I'm Looking For
            item {
                Text(
                    "What I'm Looking For",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(12.dp))

                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            navController?.navigate("editConnectionGoal")
                        },
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // AFFICHAGE INSTANTANÉ - Plus de loading
                            Text(getEmojiForConnectionGoal(userConnectionGoal), fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                if (userConnectionGoal.isNotEmpty()) userConnectionGoal else "Non spécifié",
                                fontSize = 16.sp,
                                color = Color.Black
                            )
                        }
                        Text(">", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))
            }

            // Section Essential Info
            item {
                Text(
                    "Essential Info",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Nom
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            navController?.navigate("editName")
                        },
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            userName,
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                        Text(">", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Date de naissance
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            if (userBirthday.isNotEmpty()) userBirthday else "Non spécifié",
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                        Text("▼", fontSize = 14.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Pays
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFE8E8E8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        if (userNationality.isNotEmpty()) userNationality else "Non spécifié",
                        modifier = Modifier.padding(16.dp),
                        fontSize = 16.sp,
                        color = Color.Gray
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Langue
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFE8E8E8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        if (userNativeLanguage.isNotEmpty()) userNativeLanguage else "Non spécifié",
                        modifier = Modifier.padding(16.dp),
                        fontSize = 16.sp,
                        color = Color.Gray
                    )
                }

                Spacer(modifier = Modifier.height(32.dp))
            }

            // Section Basic Info
            item {
                Text(
                    "Basic Info",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Height
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("📏", fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                "Height",
                                fontSize = 16.sp,
                                color = Color.Black
                            )
                        }
                        Text(">", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Occupation
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("💼", fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                "Occupation",
                                fontSize = 16.sp,
                                color = Color.Black
                            )
                        }
                        Text(">", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Education
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("🎓", fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                "Education",
                                fontSize = 16.sp,
                                color = Color.Black
                            )
                        }
                        Text(">", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))
            }

            // Section Interested Languages
            item {
                Text(
                    "Interested Languages",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            navController?.navigate("editInterestLanguages")
                        },
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (userInterestLanguages.isNotEmpty()) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            userInterestLanguages.take(2).forEach { language ->
                                Card(
                                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F0F0)),
                                    shape = RoundedCornerShape(20.dp)
                                ) {
                                    Text(
                                        "${language["name"]} Lv.${language["level"]}",
                                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                                        fontSize = 14.sp,
                                        color = Color.Black
                                    )
                                }
                            }
                        }
                    } else {
                        Text(
                            "Aucune langue d'intérêt",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }

                    Text(">", fontSize = 18.sp, color = Color.Gray)
                }

                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}

@Composable
fun ViewPhotosScreen(
    authManager: AuthManager,
    onBack: () -> Unit
) {
    var photoUrls by remember { mutableStateOf<List<String>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // Charger les photos au démarrage
    LaunchedEffect(Unit) {
        try {
            isLoading = true
            photoUrls = authManager.getPublicPhotoUrls()
            isLoading = false
        } catch (e: Exception) {
            errorMessage = "Erreur lors du chargement des photos: ${e.message}"
            isLoading = false
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header avec bouton retour
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    painter = painterResource(id = android.R.drawable.ic_menu_revert),
                    contentDescription = "Retour"
                )
            }
            Text(
                "Mes Photos",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(start = 8.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        when {
            isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color(0xFFE91E63))
                }
            }

            errorMessage != null -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        errorMessage!!,
                        color = Color.Red,
                        textAlign = TextAlign.Center
                    )
                }
            }

            photoUrls.isEmpty() -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "Aucune photo trouvée",
                        fontSize = 18.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }

            else -> {
                // Grille de photos
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    contentPadding = PaddingValues(8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(photoUrls) { photoUrl ->
                        Card(
                            modifier = Modifier
                                .aspectRatio(1f)
                                .fillMaxWidth(),
                            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            AsyncImage(
                                model = photoUrl,
                                contentDescription = "Photo de profil",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun UploadPhotoScreen(authManager: AuthManager, onPhotoUploaded: () -> Unit) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // État pour les photos sélectionnées
    val selectedPhotos = remember { mutableStateListOf<Uri>() }
    var isUploading by remember { mutableStateOf(false) }

    // Launcher pour sélectionner des photos depuis la galerie
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            if (selectedPhotos.size < 6) { // Maximum 6 photos
                selectedPhotos.add(it)
            } else {
                Toast.makeText(context, "Maximum 6 photos autorisées", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // Launcher pour prendre une photo avec la caméra
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            // La photo a été prise et sauvegardée dans l'URI temporaire
            Toast.makeText(context, "Photo prise avec succès", Toast.LENGTH_SHORT).show()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(32.dp))

        Text(
            text = "Ajoutez vos photos",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1A1B1E)
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Ajoutez au moins 2 photos pour continuer",
            fontSize = 16.sp,
            color = Color(0xFF666666),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Grille des photos sélectionnées
        if (selectedPhotos.isNotEmpty()) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                items(selectedPhotos) { uri ->
                    Box(
                        modifier = Modifier.size(100.dp)
                    ) {
                        AsyncImage(
                            model = uri,
                            contentDescription = "Photo sélectionnée",
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(12.dp))
                                .border(2.dp, Color(0xFFD81B60), RoundedCornerShape(12.dp)),
                            contentScale = ContentScale.Crop
                        )

                        // Bouton supprimer
                        IconButton(
                            onClick = { selectedPhotos.remove(uri) },
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .size(24.dp)
                                .background(Color.Red, CircleShape)
                        ) {
                            Icon(
                                Icons.Default.Delete,
                                contentDescription = "Supprimer",
                                tint = Color.White,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))
        }

        // Boutons d'ajout de photos
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Bouton Galerie
            OutlinedButton(
                onClick = { galleryLauncher.launch("image/*") },
                enabled = !isUploading && selectedPhotos.size < 6,
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Galerie")
            }

            // Bouton Caméra
            OutlinedButton(
                onClick = {
                    // TODO: Implémenter la caméra
                    Toast.makeText(context, "Caméra bientôt disponible", Toast.LENGTH_SHORT).show()
                },
                enabled = !isUploading && selectedPhotos.size < 6,
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Caméra")
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Indicateur de progression
        Text(
            text = "${selectedPhotos.size}/6 photos sélectionnées",
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )

        if (selectedPhotos.size >= 2) {
            Text(
                text = "✓ Minimum atteint",
                fontSize = 14.sp,
                color = Color(0xFF4CAF50),
                fontWeight = FontWeight.Medium
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // Bouton Continuer
        Button(
            onClick = {
                if (selectedPhotos.size >= 2) {
                    isUploading = true

                    // Upload toutes les photos
                    coroutineScope.launch {
                        try {
                            val photoUrls = mutableListOf<String>()
                            val userId = authManager.getCurrentUserId()

                            if (userId == null) {
                                Toast.makeText(context, "Utilisateur non connecté", Toast.LENGTH_LONG).show()
                                isUploading = false
                                return@launch
                            }

                            // Upload séquentiel pour éviter les problèmes de concurrence
                            selectedPhotos.forEachIndexed { index, uri ->
                                val fileName = "photo_${System.currentTimeMillis()}_$index.jpg"

                                try {
                                    // Attendre la fin de chaque upload avant de continuer
                                    authManager.uploadPhoto(uri, fileName)
                                        .collect { result ->
                                            when (result) {
                                                is AuthResponse.Success -> {
                                                    // Utiliser le même chemin que dans AuthManager
                                                    val photoUrl = "profiles/$userId/$fileName"
                                                    photoUrls.add(photoUrl)
                                                    Log.d("UploadPhoto", "Photo $index uploadée: $photoUrl")
                                                }
                                                is AuthResponse.Error -> {
                                                    throw Exception("Erreur upload photo $index: ${result.message}")
                                                }
                                            }
                                        }
                                } catch (e: Exception) {
                                    Log.e("UploadPhoto", "Erreur upload photo $index", e)
                                    Toast.makeText(context, "Erreur upload photo ${index + 1}", Toast.LENGTH_SHORT).show()
                                    isUploading = false
                                    return@launch
                                }
                            }

                            // Tous les uploads ont réussi, sauvegarder les URLs
                            if (photoUrls.size == selectedPhotos.size) {
                                authManager.savePhotoUrls(photoUrls)
                                    .collect { result ->
                                        when (result) {
                                            is AuthResponse.Success -> {
                                                Toast.makeText(context, "Photos sauvegardées avec succès!", Toast.LENGTH_SHORT).show()
                                                isUploading = false
                                                onPhotoUploaded()
                                            }
                                            is AuthResponse.Error -> {
                                                Toast.makeText(context, "Erreur sauvegarde: ${result.message}", Toast.LENGTH_LONG).show()
                                                isUploading = false
                                            }
                                        }
                                    }
                            } else {
                                Toast.makeText(context, "Erreur: nombre de photos incorrect", Toast.LENGTH_LONG).show()
                                isUploading = false
                            }

                        } catch (e: Exception) {
                            Log.e("UploadPhoto", "Erreur générale upload", e)
                            Toast.makeText(context, "Erreur lors de l'upload des photos", Toast.LENGTH_LONG).show()
                            isUploading = false
                        }
                    }
                }
            },
            enabled = selectedPhotos.size >= 2 && !isUploading,
            colors = ButtonDefaults.buttonColors(
                containerColor = if (selectedPhotos.size >= 2) Color(0xFFD81B60) else Color.Gray
            ),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
        ) {
            if (isUploading) {
                CircularProgressIndicator(
                    color = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Upload en cours...")
            } else {
                Text(
                    text = if (selectedPhotos.size >= 2) "Continuer" else "Ajoutez au moins 2 photos",
                    fontSize = 18.sp
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Composable
fun SplashScreen(navController: NavHostController, authManager: AuthManager) {
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(Unit) {
        coroutineScope.launch {
            // Initialiser la session Supabase d'abord
            authManager.initializeSession()

            if (authManager.isLoggedIn()) {
                val step = authManager.getOnboardingStep()
                Log.d("SplashScreen", "Valeur onboarding_step = '$step'")

                when (step) {
                    // Si onboarding_step est "complete", aller à l'accueil
                    "complete" -> {
                        Log.d("SplashScreen", "Navigation vers home - onboarding terminé")
                        navController.navigate("home") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "photo", aller à uploadPhoto
                    "photo" -> {
                        Log.d("SplashScreen", "Navigation vers uploadPhoto - étape photo")
                        navController.navigate("uploadPhoto") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "nationality", aller à selectNationality
                    "nationality" -> {
                        Log.d("SplashScreen", "Navigation vers selectNationality - étape nationalité")
                        navController.navigate("selectNationality") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "native_language", aller à selectNativeLanguage
                    "native_language" -> {
                        Log.d("SplashScreen", "Navigation vers selectNativeLanguage - étape langue natale")
                        navController.navigate("selectNativeLanguage") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "interest_languages", aller à selectInterestLanguages
                    "interest_languages" -> {
                        Log.d("SplashScreen", "Navigation vers selectInterestLanguages - étape langues d'intérêt")
                        navController.navigate("selectInterestLanguages") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "connection_goals", aller à selectConnectionGoals
                    "connection_goals" -> {
                        Log.d("SplashScreen", "Navigation vers selectConnectionGoals - étape objectifs")
                        navController.navigate("selectConnectionGoals") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "interests", aller à selectInterests
                    "interests" -> {
                        Log.d("SplashScreen", "Navigation vers selectInterests - étape intérêts")
                        navController.navigate("selectInterests") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si onboarding_step est "bio", aller à writeBio
                    "bio" -> {
                        Log.d("SplashScreen", "Navigation vers writeBio - étape bio")
                        navController.navigate("writeBio") { popUpTo("splash") { inclusive = true } }
                    }
                    // Si pas d'onboarding_step ou autre valeur, vérifier si le profil est complet
                    else -> {
                        val isProfileComplete = authManager.isProfileComplete()
                        Log.d("SplashScreen", "Profil complet = $isProfileComplete")

                        if (isProfileComplete) {
                            Log.d("SplashScreen", "Navigation vers uploadPhoto - profil complet mais pas d'étape")
                            navController.navigate("uploadPhoto") { popUpTo("splash") { inclusive = true } }
                        } else {
                            Log.d("SplashScreen", "Navigation vers profile - profil incomplet")
                            navController.navigate("profile") { popUpTo("splash") { inclusive = true } }
                        }
                    }
                }
            } else {
                navController.navigate("welcome") { popUpTo("splash") { inclusive = true } }
            }
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
fun SelectNationalityScreen(
    authManager: AuthManager,
    onNationalitySelected: (String) -> Unit,
    onBack: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    var searchQuery by remember { mutableStateOf("") }
    var selectedNationality by remember { mutableStateOf("France") } // Défaut

    // Mettre à jour l'étape d'onboarding dès que la page se charge
    LaunchedEffect(Unit) {
        authManager.updateOnboardingStep("nationality")
            .collect { result ->
                when (result) {
                    is AuthResponse.Success -> {
                        Log.d("SelectNationality", "Étape mise à jour: nationality")
                    }
                    is AuthResponse.Error -> {
                        Log.e("SelectNationality", "Erreur mise à jour étape: ${result.message}")
                    }
                }
            }
    }

    // Liste des nationalités (échantillon)
    val nationalities = listOf(
        "France", "Gabon", "Ghana", "Gibraltar", "Guadeloupe", "Guam", "Guatemala",
        "Guinée", "Guinée-Bissau", "Guyane", "Haïti", "Honduras", "Hong Kong",
        "Hongrie", "Inde", "Indonésie", "Iran", "Iraq", "Irlande", "Islande",
        "Israël", "Italie", "Jamaïque", "Japon", "Jordanie", "Kazakhstan",
        "Kenya", "Kirghizistan", "Kiribati", "Koweït", "Laos", "Lesotho",
        "Lettonie", "Liban", "Liberia", "Libye", "Liechtenstein", "Lituanie",
        "Luxembourg", "Macao", "Macédoine", "Madagascar", "Malaisie", "Malawi",
        "Maldives", "Mali", "Malte", "Maroc", "Marshall", "Martinique",
        "Maurice", "Mauritanie", "Mayotte", "Mexique", "Micronésie", "Moldavie",
        "Monaco", "Mongolie", "Monténégro", "Montserrat", "Mozambique", "Myanmar"
    )

    // Filtrer les nationalités selon la recherche
    val filteredNationalities = if (searchQuery.isEmpty()) {
        nationalities
    } else {
        nationalities.filter { it.contains(searchQuery, ignoreCase = true) }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Titre
        Text(
            text = "Veuillez sélectionner\nvotre nationalité.",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            lineHeight = 34.sp
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Sous-titre avec icône
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Cela ne pourra pas être changé plus tard,\ndonc choisissez attentivement.",
                fontSize = 16.sp,
                color = Color.Gray,
                lineHeight = 22.sp
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("⚠️", fontSize = 16.sp)
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Barre de recherche
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            placeholder = { Text("Rechercher la nationalité", color = Color.Gray) },
            leadingIcon = {
                Icon(
                    painter = painterResource(id = android.R.drawable.ic_menu_search),
                    contentDescription = "Rechercher",
                    tint = Color.Gray
                )
            },
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF5F5F5), RoundedCornerShape(12.dp)),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color.Transparent,
                unfocusedBorderColor = Color.Transparent,
                focusedContainerColor = Color(0xFFF5F5F5),
                unfocusedContainerColor = Color(0xFFF5F5F5)
            ),
            shape = RoundedCornerShape(12.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Liste des nationalités
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(filteredNationalities) { nationality ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { selectedNationality = nationality }
                        .padding(vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Icône de sélection
                    if (selectedNationality == nationality) {
                        Icon(
                            painter = painterResource(id = android.R.drawable.checkbox_on_background),
                            contentDescription = "Sélectionné",
                            tint = Color(0xFFE91E63),
                            modifier = Modifier.size(24.dp)
                        )
                    } else {
                        Icon(
                            painter = painterResource(id = android.R.drawable.checkbox_off_background),
                            contentDescription = "Non sélectionné",
                            tint = Color.Gray,
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Text(
                        text = nationality,
                        fontSize = 16.sp,
                        color = Color.Black
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Boutons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Bouton Retour
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Gray
                ),
                border = BorderStroke(1.dp, Color.Gray),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "Retour",
                    fontSize = 16.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Bouton Suivant
            Button(
                onClick = {
                    coroutineScope.launch {
                        authManager.saveNationality(selectedNationality)
                            .collect { result ->
                                when (result) {
                                    is AuthResponse.Success -> {
                                        onNationalitySelected(selectedNationality)
                                    }
                                    is AuthResponse.Error -> {
                                        Toast.makeText(context, "Erreur: ${result.message}", Toast.LENGTH_LONG).show()
                                    }
                                }
                            }
                    }
                },
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE91E63)
                ),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "SUIVANT",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}

@Composable
fun SelectNativeLanguageScreen(
    authManager: AuthManager,
    onLanguageSelected: (String) -> Unit,
    onBack: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    var searchQuery by remember { mutableStateOf("") }
    var selectedLanguage by remember { mutableStateOf("français") } // Défaut

    // Mettre à jour l'étape d'onboarding dès que la page se charge
    LaunchedEffect(Unit) {
        authManager.updateOnboardingStep("native_language")
            .collect { result ->
                when (result) {
                    is AuthResponse.Success -> {
                        Log.d("SelectNativeLanguage", "Étape mise à jour: native_language")
                    }
                    is AuthResponse.Error -> {
                        Log.e("SelectNativeLanguage", "Erreur mise à jour étape: ${result.message}")
                    }
                }
            }
    }

    // Liste des langues natales
    val languages = listOf(
        "français", "frison occidental", "féroïen", "galicien", "gallois", "ganda",
        "gaélique écossais", "géorgien", "allemand", "grec", "guarani", "gujarati",
        "créole haïtien", "haoussa", "hébreu", "hindi", "hmong", "hongrois",
        "islandais", "igbo", "indonésien", "irlandais", "italien", "japonais",
        "javanais", "kannada", "kazakh", "khmer", "kinyarwanda", "coréen",
        "kurde", "kirghize", "lao", "latin", "letton", "lituanien", "luxembourgeois",
        "macédonien", "malgache", "malais", "malayalam", "maltais", "maori",
        "marathi", "mongol", "birman", "népalais", "norvégien", "odia",
        "pachto", "persan", "polonais", "portugais", "pendjabi", "roumain",
        "russe", "samoan", "gaélique écossais", "serbe", "sesotho", "shona",
        "sindhi", "cingalais", "slovaque", "slovène", "somali", "espagnol"
    )

    // Filtrer les langues selon la recherche
    val filteredLanguages = if (searchQuery.isEmpty()) {
        languages
    } else {
        languages.filter { it.contains(searchQuery, ignoreCase = true) }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Titre
        Text(
            text = "Veuillez sélectionner\nvotre langue natale.",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            lineHeight = 34.sp
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Sous-titre avec icône
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Ceci ne peut pas être modifié plus tard,\nveuillez donc sélectionner soigneusement",
                fontSize = 16.sp,
                color = Color.Gray,
                lineHeight = 22.sp
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("⚠️", fontSize = 16.sp)
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Barre de recherche
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            placeholder = { Text("Rechercher la nationalité", color = Color.Gray) },
            leadingIcon = {
                Icon(
                    painter = painterResource(id = android.R.drawable.ic_menu_search),
                    contentDescription = "Rechercher",
                    tint = Color.Gray
                )
            },
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF5F5F5), RoundedCornerShape(12.dp)),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color.Transparent,
                unfocusedBorderColor = Color.Transparent,
                focusedContainerColor = Color(0xFFF5F5F5),
                unfocusedContainerColor = Color(0xFFF5F5F5)
            ),
            shape = RoundedCornerShape(12.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Liste des langues
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(filteredLanguages) { language ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { selectedLanguage = language }
                        .padding(vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Icône de sélection
                    if (selectedLanguage == language) {
                        Icon(
                            painter = painterResource(id = android.R.drawable.checkbox_on_background),
                            contentDescription = "Sélectionné",
                            tint = Color(0xFFE91E63),
                            modifier = Modifier.size(24.dp)
                        )
                    } else {
                        Icon(
                            painter = painterResource(id = android.R.drawable.checkbox_off_background),
                            contentDescription = "Non sélectionné",
                            tint = Color.Gray,
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Text(
                        text = language,
                        fontSize = 16.sp,
                        color = Color.Black
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Boutons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Bouton Retour
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Gray
                ),
                border = BorderStroke(1.dp, Color.Gray),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "Retour",
                    fontSize = 16.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Bouton Suivant
            Button(
                onClick = {
                    coroutineScope.launch {
                        authManager.saveNativeLanguage(selectedLanguage)
                            .collect { result ->
                                when (result) {
                                    is AuthResponse.Success -> {
                                        onLanguageSelected(selectedLanguage)
                                    }
                                    is AuthResponse.Error -> {
                                        Toast.makeText(context, "Erreur: ${result.message}", Toast.LENGTH_LONG).show()
                                    }
                                }
                            }
                    }
                },
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE91E63)
                ),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "SUIVANT",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}



@Composable
fun SelectInterestLanguagesScreen(
    authManager: AuthManager,
    onLanguagesSelected: (List<InterestLanguage>) -> Unit,
    onBack: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    var selectedLanguages by remember { mutableStateOf<List<InterestLanguage>>(emptyList()) }
    var showLanguageModal by remember { mutableStateOf(false) }
    var showLevelModal by remember { mutableStateOf(false) }
    var selectedLanguageForLevel by remember { mutableStateOf("") }

    // Mettre à jour l'étape d'onboarding dès que la page se charge
    LaunchedEffect(Unit) {
        authManager.updateOnboardingStep("interest_languages")
            .collect { result ->
                when (result) {
                    is AuthResponse.Success -> {
                        Log.d("SelectInterestLanguages", "Étape mise à jour: interest_languages")
                    }
                    is AuthResponse.Error -> {
                        Log.e("SelectInterestLanguages", "Erreur mise à jour étape: ${result.message}")
                    }
                }
            }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Titre
        Text(
            text = "Veuillez sélectionner\nla langue\nqui vous intéresse",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            lineHeight = 34.sp
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Sous-titre
        Text(
            text = "Partagez les langues qui t'intéressent avec des\namis.\nSélectionnez jusqu'à 5 options",
            fontSize = 16.sp,
            color = Color.Gray,
            lineHeight = 22.sp
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Langues sélectionnées
        if (selectedLanguages.isNotEmpty()) {
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(selectedLanguages) { language ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "${language.name} Lv.${language.level}",
                                fontSize = 16.sp,
                                color = Color.Black
                            )

                            IconButton(
                                onClick = {
                                    selectedLanguages = selectedLanguages.filter { it.name != language.name }
                                }
                            ) {
                                Text("✕", fontSize = 16.sp, color = Color.Gray)
                            }
                        }
                    }
                }
            }
        } else {
            Spacer(modifier = Modifier.weight(1f))
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Bouton Ajouter une langue
        OutlinedButton(
            onClick = { showLanguageModal = true },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = Color(0xFFE91E63)
            ),
            border = BorderStroke(2.dp, Color(0xFFE91E63)),
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 8.dp)
            ) {
                Text(
                    "➕",
                    fontSize = 16.sp,
                    color = Color(0xFFE91E63)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    "Ajouter une langue",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Boutons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Bouton Retour
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Gray
                ),
                border = BorderStroke(1.dp, Color.Gray),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "Retour",
                    fontSize = 16.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Bouton Suivant
            Button(
                onClick = {
                    coroutineScope.launch {
                        authManager.saveInterestLanguages(selectedLanguages)
                            .collect { result ->
                                when (result) {
                                    is AuthResponse.Success -> {
                                        onLanguagesSelected(selectedLanguages)
                                    }
                                    is AuthResponse.Error -> {
                                        Toast.makeText(context, "Erreur: ${result.message}", Toast.LENGTH_LONG).show()
                                    }
                                }
                            }
                    }
                },
                enabled = selectedLanguages.isNotEmpty(),
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (selectedLanguages.isNotEmpty()) Color(0xFFE91E63) else Color.Gray
                ),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "SUIVANT",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }

    // Modal de sélection de langue
    if (showLanguageModal) {
        LanguageSelectionModal(
            onLanguageSelected = { language ->
                selectedLanguageForLevel = language
                showLanguageModal = false
                showLevelModal = true
            },
            onDismiss = { showLanguageModal = false },
            selectedLanguages = selectedLanguages.map { it.name }
        )
    }

    // Modal de sélection de niveau
    if (showLevelModal) {
        LanguageLevelModal(
            language = selectedLanguageForLevel,
            onLevelSelected = { level ->
                if (selectedLanguages.size < 5) {
                    selectedLanguages = selectedLanguages + InterestLanguage(selectedLanguageForLevel, level)
                }
                showLevelModal = false
                selectedLanguageForLevel = ""
            },
            onDismiss = {
                showLevelModal = false
                selectedLanguageForLevel = ""
            }
        )
    }
}

@Composable
fun LanguageSelectionModal(
    onLanguageSelected: (String) -> Unit,
    onDismiss: () -> Unit,
    selectedLanguages: List<String>
) {
    var searchQuery by remember { mutableStateOf("") }

    // Langues populaires
    val popularLanguages = listOf("anglais", "espagnol", "coréen", "japonais", "chinois")

    // Toutes les langues
    val allLanguages = listOf(
        "abkhaze", "afar", "afrikaans", "akan", "albanais", "allemand", "amharique",
        "anglais", "arabe", "aragonais", "arménien", "assamais", "avar", "aymara",
        "azéri", "bachkir", "bambara", "basque", "bengali", "biélorusse", "birman",
        "bosniaque", "breton", "bulgare", "catalan", "chinois", "coréen", "croate",
        "danois", "espagnol", "estonien", "finnois", "français", "galicien", "gallois",
        "géorgien", "grec", "gujarati", "hébreu", "hindi", "hongrois", "islandais",
        "indonésien", "irlandais", "italien", "japonais", "javanais", "kannada",
        "kazakh", "khmer", "kirghize", "lao", "letton", "lituanien", "luxembourgeois",
        "macédonien", "malais", "malgache", "maltais", "norvégien", "néerlandais",
        "ourdou", "pachto", "persan", "polonais", "portugais", "roumain", "russe",
        "serbe", "slovaque", "slovène", "suédois", "swahili", "tamoul", "tchèque",
        "thaï", "turc", "ukrainien", "vietnamien", "wolof", "yoruba", "zoulou"
    )

    // Filtrer les langues disponibles (non sélectionnées)
    val availableLanguages = allLanguages.filter { it !in selectedLanguages }
    val filteredLanguages = if (searchQuery.isEmpty()) {
        availableLanguages
    } else {
        availableLanguages.filter { it.contains(searchQuery, ignoreCase = true) }
    }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.8f),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        "Ajouter la langue qui vous\nintéresse",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )

                    IconButton(onClick = onDismiss) {
                        Text("✕", fontSize = 18.sp, color = Color.Gray)
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Barre de recherche
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    placeholder = { Text("Recherche langue", color = Color.Gray) },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = android.R.drawable.ic_menu_search),
                            contentDescription = "Rechercher",
                            tint = Color.Gray
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFF5F5F5), RoundedCornerShape(12.dp)),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedContainerColor = Color(0xFFF5F5F5),
                        unfocusedContainerColor = Color(0xFFF5F5F5)
                    ),
                    shape = RoundedCornerShape(12.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Langues populaires
                if (searchQuery.isEmpty()) {
                    Text(
                        "Langues populaires",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Black
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    popularLanguages.filter { it !in selectedLanguages }.forEach { language ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { onLanguageSelected(language) }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(id = android.R.drawable.checkbox_off_background),
                                contentDescription = "Sélectionner",
                                tint = Color.Gray,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(language, fontSize = 16.sp, color = Color.Black)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("⚠️", fontSize = 12.sp)
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        "Toutes les langues",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Black
                    )

                    Spacer(modifier = Modifier.height(12.dp))
                }

                // Liste des langues
                LazyColumn(
                    modifier = Modifier.weight(1f)
                ) {
                    items(filteredLanguages) { language ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { onLanguageSelected(language) }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(id = android.R.drawable.checkbox_off_background),
                                contentDescription = "Sélectionner",
                                tint = Color.Gray,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(language, fontSize = 16.sp, color = Color.Black)
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Texte en bas
                Text(
                    "Critères de niveau de langue",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Bouton Postuler
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFE91E63)
                    ),
                    shape = RoundedCornerShape(25.dp)
                ) {
                    Text(
                        "Postuler(0/5)",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun LanguageLevelModal(
    language: String,
    onLevelSelected: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedLevel by remember { mutableStateOf(1) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Titre
                Text(
                    "Sélectionnez votre niveau\nen $language",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Sélecteur de niveau (1-5)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    for (level in 1..5) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Card(
                                modifier = Modifier
                                    .size(48.dp)
                                    .clickable { selectedLevel = level },
                                colors = CardDefaults.cardColors(
                                    containerColor = if (selectedLevel == level) Color(0xFFE91E63) else Color(0xFFF5F5F5)
                                ),
                                shape = CircleShape
                            ) {
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = level.toString(),
                                        fontSize = 18.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = if (selectedLevel == level) Color.White else Color.Black
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = when (level) {
                                    1 -> "Débutant"
                                    2 -> "Élémentaire"
                                    3 -> "Intermédiaire"
                                    4 -> "Avancé"
                                    5 -> "Expert"
                                    else -> ""
                                },
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Boutons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Bouton Annuler
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        ),
                        border = BorderStroke(1.dp, Color.Gray),
                        shape = RoundedCornerShape(25.dp)
                    ) {
                        Text(
                            "Annuler",
                            fontSize = 16.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }

                    // Bouton Confirmer
                    Button(
                        onClick = { onLevelSelected(selectedLevel) },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFE91E63)
                        ),
                        shape = RoundedCornerShape(25.dp)
                    ) {
                        Text(
                            "Confirmer",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SelectConnectionGoalsScreen(
    authManager: AuthManager,
    onGoalSelected: (String) -> Unit,
    onBack: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    var selectedGoal by remember { mutableStateOf<String?>(null) }

    // Mettre à jour l'étape d'onboarding dès que la page se charge
    LaunchedEffect(Unit) {
        authManager.updateOnboardingStep("connection_goals")
            .collect { result ->
                when (result) {
                    is AuthResponse.Success -> {
                        Log.d("SelectConnectionGoals", "Étape mise à jour: connection_goals")
                    }
                    is AuthResponse.Error -> {
                        Log.e("SelectConnectionGoals", "Erreur mise à jour étape: ${result.message}")
                    }
                }
            }
    }

    // Options d'objectifs avec icônes
    val goals = listOf(
        "Engagé" to "💕",
        "Rencontre casual" to "🌸",
        "Échange langue" to "💬",
        "Amis proches" to "🌍",
        "Pote de voyage" to "🔒",
        "Esprit ouvert" to "🧠"
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Titre
        Text(
            text = "Vous cherchez\nune connexion\nspécifique ?",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            lineHeight = 34.sp
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Sous-titre
        Text(
            text = "Indiquez votre objectif. Vous pourrez le\nmodifier plus tard.",
            fontSize = 16.sp,
            color = Color.Gray,
            lineHeight = 22.sp
        )

        Spacer(modifier = Modifier.height(48.dp))

        // Grille des objectifs (2x3)
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.weight(1f)
        ) {
            items(goals) { (goal, icon) ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp)
                        .clickable { selectedGoal = goal },
                    colors = CardDefaults.cardColors(
                        containerColor = if (selectedGoal == goal) Color(0xFFE91E63).copy(alpha = 0.1f) else Color(0xFFF8F8F8)
                    ),
                    border = if (selectedGoal == goal) BorderStroke(2.dp, Color(0xFFE91E63)) else null,
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = icon,
                            fontSize = 32.sp
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = goal,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Boutons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Bouton Retour
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Gray
                ),
                border = BorderStroke(1.dp, Color.Gray),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "Retour",
                    fontSize = 16.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Bouton Suivant (OBLIGATOIRE - désactivé si rien sélectionné)
            Button(
                onClick = {
                    selectedGoal?.let { goal ->
                        coroutineScope.launch {
                            authManager.saveConnectionGoal(goal)
                                .collect { result ->
                                    when (result) {
                                        is AuthResponse.Success -> {
                                            onGoalSelected(goal)
                                        }
                                        is AuthResponse.Error -> {
                                            Toast.makeText(context, "Erreur: ${result.message}", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                }
                        }
                    }
                },
                enabled = selectedGoal != null,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (selectedGoal != null) Color(0xFFE91E63) else Color.Gray
                ),
                shape = RoundedCornerShape(25.dp)
            ) {
                Text(
                    "SUIVANT",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}

// Fonction pour obtenir l'emoji selon l'intérêt
fun getEmojiForInterest(interest: String): String {
    return when (interest.lowercase()) {
        "soccer", "football" -> "⚽"
        "basketball" -> "🏀"
        "baseball" -> "⚾"
        "golf" -> "🏌️"
        "tennis" -> "🎾"
        "volleyball" -> "🏐"
        "swimming", "natation" -> "🏊"
        "running", "course" -> "🏃"
        "cycling", "vélo" -> "🚴"
        "fitness", "gym" -> "💪"
        "yoga" -> "🧘"
        "dance", "danse" -> "💃"
        "music", "musique" -> "🎵"
        "guitar", "guitare" -> "🎸"
        "piano" -> "🎹"
        "singing", "chant" -> "🎤"
        "reading", "lecture" -> "📚"
        "writing", "écriture" -> "✍️"
        "photography", "photo" -> "📸"
        "art", "painting" -> "🎨"
        "cooking", "cuisine" -> "👨‍🍳"
        "travel", "voyage" -> "✈️"
        "movies", "films" -> "🎬"
        "gaming", "jeux" -> "🎮"
        "technology", "tech" -> "💻"
        "science" -> "🔬"
        "nature" -> "🌿"
        "animals", "animaux" -> "🐾"
        "coffee", "café" -> "☕"
        "wine", "vin" -> "🍷"
        "beer", "bière" -> "🍺"
        else -> "🎯"
    }
}

// Fonction pour obtenir l'emoji selon l'objectif de connexion
fun getEmojiForConnectionGoal(goal: String): String {
    return when (goal.lowercase()) {
        "engagé" -> "💕"
        "rencontre casual" -> "🌸"
        "échange langue" -> "💬"
        "amis proches" -> "🌍"
        "pote de voyage" -> "🔒"
        "esprit ouvert" -> "🧠"
        else -> "💫"
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditNameScreen(
    authManager: AuthManager,
    onNameSaved: (String) -> Unit,
    onBack: () -> Unit
) {
    var name by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    var isSaving by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // Charger le nom actuel
    LaunchedEffect(Unit) {
        name = authManager.getUserName()
        isLoading = false
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Header avec bouton retour
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Retour",
                    tint = Color.Black
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                "Modifier le nom",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFFE91E63))
            }
        } else {
            // Champ de saisie du nom
            OutlinedTextField(
                value = name,
                onValueChange = { name = it },
                label = { Text("Nom") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFFE91E63),
                    focusedLabelColor = Color(0xFFE91E63)
                ),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Bouton sauvegarder
            Button(
                onClick = {
                    if (name.isNotBlank()) {
                        isSaving = true
                        coroutineScope.launch {
                            authManager.updateUserName(name).collect { result ->
                                when (result) {
                                    is AuthResponse.Success -> {
                                        Toast.makeText(context, "Nom mis à jour !", Toast.LENGTH_SHORT).show()
                                        onNameSaved(name)
                                    }
                                    is AuthResponse.Error -> {
                                        Toast.makeText(context, "Erreur: ${result.message}", Toast.LENGTH_LONG).show()
                                        isSaving = false
                                    }
                                }
                            }
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE91E63)
                ),
                enabled = name.isNotBlank() && !isSaving
            ) {
                if (isSaving) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                } else {
                    Text(
                        "Sauvegarder",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

// ÉCRANS DE MODIFICATION SIMPLES
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditBioScreen(
    authManager: AuthManager,
    onBioSaved: (String) -> Unit,
    onBack: () -> Unit
) {
    var bio by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    var isSaving by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(Unit) {
        bio = authManager.getUserBio()
        isLoading = false
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Retour", tint = Color.Black)
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text("Modifier l'introduction", fontSize = 20.sp, fontWeight = FontWeight.Bold, color = Color.Black)
        }

        Spacer(modifier = Modifier.height(32.dp))

        if (isLoading) {
            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                CircularProgressIndicator(color = Color(0xFFE91E63))
            }
        } else {
            OutlinedTextField(
                value = bio,
                onValueChange = { bio = it },
                label = { Text("Introduction") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFFE91E63),
                    focusedLabelColor = Color(0xFFE91E63)
                ),
                maxLines = 5
            )

            Spacer(modifier = Modifier.height(32.dp))

            Button(
                onClick = {
                    if (bio.isNotBlank()) {
                        isSaving = true
                        coroutineScope.launch {
                            // TODO: Ajouter fonction updateUserBio dans AuthManager
                            Toast.makeText(context, "Bio mise à jour !", Toast.LENGTH_SHORT).show()
                            onBioSaved(bio)
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth().height(50.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE91E63)),
                enabled = bio.isNotBlank() && !isSaving
            ) {
                if (isSaving) {
                    CircularProgressIndicator(color = Color.White, modifier = Modifier.size(20.dp))
                } else {
                    Text("Sauvegarder", color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Medium)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditConnectionGoalScreen(
    authManager: AuthManager,
    onGoalSaved: (String) -> Unit,
    onBack: () -> Unit
) {
    var selectedGoal by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    val goals = listOf("Engagé", "Rencontre casual", "Échange langue", "Amis proches", "Pote de voyage", "Esprit ouvert")

    LaunchedEffect(Unit) {
        selectedGoal = authManager.getUserConnectionGoal()
        isLoading = false
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Retour", tint = Color.Black)
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text("Modifier l'objectif", fontSize = 20.sp, fontWeight = FontWeight.Bold, color = Color.Black)
        }

        Spacer(modifier = Modifier.height(32.dp))

        if (isLoading) {
            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                CircularProgressIndicator(color = Color(0xFFE91E63))
            }
        } else {
            goals.forEach { goal ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp)
                        .clickable { selectedGoal = goal },
                    colors = CardDefaults.cardColors(
                        containerColor = if (selectedGoal == goal) Color(0xFFE91E63) else Color(0xFFF8F8F8)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(getEmojiForConnectionGoal(goal), fontSize = 16.sp)
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            goal,
                            fontSize = 16.sp,
                            color = if (selectedGoal == goal) Color.White else Color.Black,
                            fontWeight = if (selectedGoal == goal) FontWeight.Medium else FontWeight.Normal
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            Button(
                onClick = {
                    coroutineScope.launch {
                        // TODO: Ajouter fonction updateUserConnectionGoal dans AuthManager
                        Toast.makeText(context, "Objectif mis à jour !", Toast.LENGTH_SHORT).show()
                        onGoalSaved(selectedGoal)
                    }
                },
                modifier = Modifier.fillMaxWidth().height(50.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE91E63)),
                enabled = selectedGoal.isNotEmpty()
            ) {
                Text("Sauvegarder", color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Medium)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditInterestLanguagesScreen(
    authManager: AuthManager,
    navController: NavHostController?,
    onLanguagesSaved: (List<Map<String, Any>>) -> Unit,
    onBack: () -> Unit
) {
    var selectedLanguages by remember { mutableStateOf<List<Map<String, Any>>>(emptyList()) }
    var searchQuery by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // Charger les langues actuelles
    LaunchedEffect(Unit) {
        selectedLanguages = authManager.getUserInterestLanguages()
        isLoading = false
    }

    // Liste de toutes les langues disponibles
    val allLanguages = listOf(
        "anglais", "français", "espagnol", "allemand", "italien", "portugais", "russe",
        "chinois", "japonais", "coréen", "arabe", "hindi", "bengali", "néerlandais",
        "suédois", "norvégien", "danois", "finnois", "grec", "turc", "hébreu",
        "thaï", "vietnamien", "indonésien", "malais", "tagalog", "swahili", "zoulou"
    )

    // Filtrer les langues disponibles (non sélectionnées)
    val availableLanguages = allLanguages.filter { language ->
        selectedLanguages.none { it["name"] == language }
    }
    val filteredLanguages = if (searchQuery.isEmpty()) {
        availableLanguages
    } else {
        availableLanguages.filter { it.contains(searchQuery, ignoreCase = true) }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Header avec bouton retour
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Retour", tint = Color.Black)
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text("Modifier les langues", fontSize = 20.sp, fontWeight = FontWeight.Bold, color = Color.Black)
        }

        Spacer(modifier = Modifier.height(32.dp))

        if (isLoading) {
            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                CircularProgressIndicator(color = Color(0xFFE91E63))
            }
        } else {
            // Titre
            Text(
                text = "Sélectionnez vos langues\nd'intérêt",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                lineHeight = 30.sp
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Sous-titre
            Text(
                text = "Sélectionnez jusqu'à 5 langues avec leur niveau",
                fontSize = 16.sp,
                color = Color.Gray,
                lineHeight = 22.sp
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Langues sélectionnées
            if (selectedLanguages.isNotEmpty()) {
                Text(
                    "Langues sélectionnées (${selectedLanguages.size}/5)",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )

                Spacer(modifier = Modifier.height(12.dp))

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(selectedLanguages) { language ->
                        Card(
                            colors = CardDefaults.cardColors(containerColor = Color(0xFFE91E63)),
                            shape = RoundedCornerShape(20.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    "${language["name"]} Lv.${language["level"]}",
                                    color = Color.White,
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    "×",
                                    color = Color.White,
                                    fontSize = 16.sp,
                                    modifier = Modifier.clickable {
                                        selectedLanguages = selectedLanguages.filter { it != language }
                                    }
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))
            }

            // Barre de recherche
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                placeholder = { Text("Rechercher une langue", color = Color.Gray) },
                leadingIcon = {
                    Icon(
                        painter = painterResource(id = android.R.drawable.ic_menu_search),
                        contentDescription = "Rechercher",
                        tint = Color.Gray
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFF5F5F5), RoundedCornerShape(12.dp)),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color.Transparent,
                    unfocusedBorderColor = Color.Transparent,
                    focusedContainerColor = Color(0xFFF5F5F5),
                    unfocusedContainerColor = Color(0xFFF5F5F5)
                ),
                shape = RoundedCornerShape(12.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Liste des langues disponibles
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(filteredLanguages) { language ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                if (selectedLanguages.size < 5) {
                                    // Naviguer vers sélection de niveau
                                    navController?.navigate("selectLanguageLevel/$language")
                                }
                            },
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F8F8)),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            language.replaceFirstChar { it.uppercase() },
                            modifier = Modifier.padding(16.dp),
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Bouton sauvegarder
            Button(
                onClick = {
                    coroutineScope.launch {
                        authManager.updateUserInterestLanguages(selectedLanguages).collect { result ->
                            when (result) {
                                is AuthResponse.Success -> {
                                    Toast.makeText(context, "Langues mises à jour !", Toast.LENGTH_SHORT).show()
                                    onLanguagesSaved(selectedLanguages)
                                }
                                is AuthResponse.Error -> {
                                    Toast.makeText(context, "Erreur: ${result.message}", Toast.LENGTH_LONG).show()
                                }
                            }
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth().height(50.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE91E63)),
                enabled = selectedLanguages.isNotEmpty()
            ) {
                Text("Sauvegarder", color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Medium)
            }
        }
    }
}

// ÉCRAN DE SÉLECTION DE NIVEAU DE LANGUE
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectLanguageLevelScreen(
    language: String,
    onLevelSelected: (String, Int) -> Unit,
    onBack: () -> Unit
) {
    var selectedLevel by remember { mutableStateOf(1) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        Spacer(modifier = Modifier.height(40.dp))

        // Header avec bouton retour
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Retour", tint = Color.Black)
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text("Niveau de langue", fontSize = 20.sp, fontWeight = FontWeight.Bold, color = Color.Black)
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Titre avec langue sélectionnée
        Text(
            text = "Quel est votre niveau en\n${language.replaceFirstChar { it.uppercase() }} ?",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            lineHeight = 30.sp
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Sélection des niveaux (1-5)
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            for (level in 1..5) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { selectedLevel = level },
                    colors = CardDefaults.cardColors(
                        containerColor = if (selectedLevel == level) Color(0xFFE91E63) else Color(0xFFF8F8F8)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            "Niveau $level",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            color = if (selectedLevel == level) Color.White else Color.Black
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        // Indicateur de sélection
                        if (selectedLevel == level) {
                            Text(
                                "✓",
                                fontSize = 20.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // Bouton sauvegarder
        Button(
            onClick = {
                onLevelSelected(language, selectedLevel)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE91E63))
        ) {
            Text(
                "Sauvegarder",
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }

        Spacer(modifier = Modifier.height(20.dp))
    }
}

